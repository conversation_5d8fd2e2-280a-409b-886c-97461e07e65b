# S3 Configuration Test Variables with Custom Role
# This file tests the S3 configuration source with custom execution role

config_source    = "s3"
s3_config_bucket = "coralogix-otel-config-tf-test-XXXXXXXX"  # Replace with actual bucket name from resources
s3_config_key    = "configs/otel-config.yaml"

# Custom execution role - this will be used instead of auto-created role
task_execution_role_arn = "arn:aws:iam::123456789012:role/coralogix-otel-s3-tf-test-execution-role" 