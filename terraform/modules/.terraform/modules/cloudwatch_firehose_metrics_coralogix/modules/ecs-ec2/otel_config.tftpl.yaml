receivers:
  otlp:
    protocols:
      grpc: { endpoint: 0.0.0.0:4317 }
      http: { endpoint: 0.0.0.0:4318 }

  awsecscontainermetricsd:

  prometheus:
    config:
      scrape_configs:
      - job_name: otel-collector-metrics
        scrape_interval: 30s
        static_configs:
        - targets: ["localhost:8888"]

  filelog:
    start_at: end
    force_flush_period: 0
    include:
      - /hostfs/var/lib/docker/containers/*/*.log
    include_file_name: false
    include_file_path: true
    operators:
      - type: router
        id: docker_log_json_parser
        routes:
          - output: json_parser
            expr: 'body matches "^\\{\"log\".*\\}"'
        default: move_log_file_path

      - type: json_parser
        parse_from: body
        parse_to: body
        output: recombine
        timestamp:
          parse_from: body.time
          layout: '%Y-%m-%dT%H:%M:%S.%fZ'

      # handle logs split by docker
      - type: recombine
        id: recombine
        output: move_log_file_path
        combine_field: body.log
        source_identifier: attributes["log.file.path"]
        is_last_entry: body.log endsWith "\n"
        force_flush_period: 10s	
        on_error: send
        combine_with: ""

      - type: move
        id: move_log_file_path
        from: attributes["log.file.path"]
        to: resource["log.file.path"]

  hostmetrics:
    root_path: /
    collection_interval: 10s
    scrapers:
      cpu: { metrics: { system.cpu.utilization: { enabled: true } } }
      filesystem:
        exclude_fs_types: { fs_types: [autofs, binfmt_misc, bpf, cgroup2, configfs, debugfs, devpts, devtmpfs, fusectl, hugetlbfs, iso9660, mqueue, nsfs, overlay, proc, procfs, pstore, rpc_pipefs, securityfs, selinuxfs, squashfs, sysfs, tracefs], match_type: strict }
        exclude_mount_points: { match_type: regexp, mount_points: [/dev/*, /proc/*, /sys/*, /run/k3s/containerd/*, /run/containerd/runc/*, /var/lib/docker/*, /var/lib/kubelet/*, /snap/*] }
      memory: { metrics: { system.memory.utilization: { enabled: true } } }
      process:
        metrics:
          process.cpu.utilization: { enabled: true }
          process.memory.utilization: { enabled: true }
          process.threads: { enabled: true }
        mute_process_exe_error: true
        mute_process_user_error: true


processors:
  ecsattributes/container-logs:
    container_id:
      sources:
        - "log.file.path"

  transform/logs:
    error_mode: ignore
    log_statements:
      - context: resource
        statements:
          - set(attributes["cx_container_id"], attributes["docker.id"])
          - set(attributes["aws_ecs_task_family"], attributes["aws.ecs.task.definition.family"])
          - set(attributes["image_id"], attributes["image.id"])
          - delete_key(attributes, "image.id")
    trace_statements:
      - context: span
        statements:
          - set(attributes["app.name"], "$${APP_NAME}")
          - set(attributes["subsys.name"], "$${SUB_SYS}")

  transform/entity-event:
    error_mode: silent
    log_statements:
      - context: log
        statements:
          - set(attributes["otel.entity.id"]["host.id"], resource.attributes["host.id"])
          - merge_maps(attributes, resource.attributes, "insert")
      - context: resource
        statements:
          - keep_keys(attributes, [""])

  # remove unneeded labels from metrics added as of otel v0.119.0
  transform/prometheus:
    error_mode: ignore
    metric_statements:
      - context: metric
        statements:
          - replace_pattern(name, "_total$", "")
      - context: datapoint
        statements:
          - delete_key(attributes, "otel_scope_name") where resource.attributes["service.name"] == "cdot"
          - delete_key(attributes, "service.name") where resource.attributes["service.name"] == "cdot"

      - context: resource
        statements:
          - delete_key(attributes, "service.instance.id") where attributes["service.name"] == "cdot"
          - delete_key(attributes, "service.name") where attributes["service.name"] == "cdot"

  filter/db_spanmetrics:
    traces:
      span:
      - attributes["db.system"] == nil

  batch:
    send_batch_max_size: 2048
    send_batch_size: 1024
    timeout: 1s

  resource/metadata:
    attributes:
      - action: upsert
        key: cx.otel_integration.name
        value: coralogix-integration-ecs-ec2

  # otel-collector resource detection for collector
  resourcedetection/otel-collector:
    detectors: [system, env, ecs, ec2]
    override: false
    timeout: 2s
    system:
      resource_attributes:
        host.id: { enabled: false }
        host.cpu.cache.l2.size: { enabled: true }
        host.cpu.stepping: { enabled: true }
        host.cpu.model.name: { enabled: true }
        host.cpu.model.id: { enabled: true }
        host.cpu.family: { enabled: true }
        host.cpu.vendor.id: { enabled: true }
        host.mac: { enabled: true }
        host.ip: { enabled: true }
        os.description: { enabled: true }

  resourcedetection/entity:
    detectors: [system, env, ecs, ec2]
    override: false
    timeout: 2s
    system:
      resource_attributes:
        host.id: { enabled: false }
        host.cpu.cache.l2.size: { enabled: true }
        host.cpu.stepping: { enabled: true }
        host.cpu.model.name: { enabled: true }
        host.cpu.model.id: { enabled: true }
        host.cpu.family: { enabled: true }
        host.cpu.vendor.id: { enabled: true }
        host.mac: { enabled: true }
        host.ip: { enabled: true }
        os.description: { enabled: true }

%{ if EnableHeadSampler }
  probabilistic_sampler:
    sampling_percentage: ${SamplingPercentage}
    mode: ${SamplerMode}
%{ endif }

connectors:
  forward/sampled: {}

%{ if EnableSpanMetrics }
  spanmetrics:
    dimensions:
      - name: http.method
      - name: cgx.transaction
      - name: cgx.transaction.root
      - name: status_code
      - name: db.namespace
      - name: db.operation.name
      - name: db.collection.name
      - name: db.system
      - name: http.response.status_code
      - name: rpc.grpc.status_code
      - name: service.version
    histogram:
      explicit:
        buckets:
          - 1ms
          - 4ms
          - 10ms
          - 20ms
          - 50ms
          - 100ms
          - 200ms
          - 500ms
          - 1s
          - 2s
          - 5s
    metrics_expiration: 5m
    metrics_flush_interval: '30s'
    namespace: ""
%{ endif }

%{ if EnableTracesDB }
  forward/db: {}
  spanmetrics/db:
    dimensions:
      - name: db.namespace
      - name: db.operation.name
      - name: db.collection.name
      - name: db.system
      - name: service.version
    histogram:
      explicit:
        buckets:
          - 100us
          - 1ms
          - 2ms
          - 2.5ms
          - 4ms
          - 6ms
          - 10ms
          - 100ms
          - 250ms
    metrics_expiration: 5m
    metrics_flush_interval: '30s'
    namespace: db
%{ endif }

exporters:
  coralogix:
    domain: $${CORALOGIX_DOMAIN}
    private_key: $${PRIVATE_KEY}
    application_name: $${APP_NAME}
    subsystem_name: $${SUB_SYS}
    application_name_attributes:
    - "aws.ecs.cluster"
    - "aws.ecs.task.definition.family"
    subsystem_name_attributes:
    - "aws.ecs.container.name"
    - "aws.ecs.docker.name"
    - "docker.name"
    timeout: 30s

  coralogix/resource_catalog:
    application_name: resource
    domain: $${CORALOGIX_DOMAIN}
    private_key: $${PRIVATE_KEY}
    logs:
      headers:
        X-Coralogix-Distribution: ecs-ec2-integration/1.0.0
        x-coralogix-ingress: metadata-as-otlp-logs/v1
    subsystem_name: catalog
    timeout: 30s

extensions:
  health_check:
  pprof:

service:
  extensions:
    - health_check
    - pprof

  pipelines:
    logs:
      receivers: [filelog, otlp]
      processors: [ecsattributes/container-logs, resource/metadata, transform/logs, batch]
      exporters: [coralogix]

    metrics:
      receivers: [awsecscontainermetricsd, otlp, prometheus, hostmetrics%{ if EnableSpanMetrics }, spanmetrics%{ endif }%{ if EnableTracesDB }, spanmetrics/db%{ endif }]
      processors: [resource/metadata, transform/prometheus, batch]
      exporters: [coralogix]

%{ if EnableHeadSampler }
    traces:
      receivers: [otlp]
      processors: [resource/metadata, batch]
      exporters: [forward/sampled%{ if EnableSpanMetrics }, spanmetrics%{ endif }%{ if EnableTracesDB }, forward/db%{ endif }]
      
    traces/sampled:
      receivers: [forward/sampled]
      processors: [probabilistic_sampler, batch]
      exporters: [coralogix]
%{ else }
    traces:
      receivers: [otlp]
      processors: [resource/metadata, batch]
      exporters: [%{ if EnableSpanMetrics }spanmetrics%{ endif }%{ if EnableTracesDB }, forward/db%{ endif }, coralogix]
%{ endif }

%{ if EnableTracesDB }
    traces/db:
      receivers: [forward/db]
      processors: [filter/db_spanmetrics, batch]
      exporters: [spanmetrics/db]
%{ endif }

    logs/resource_catalog:
      receivers: [hostmetrics]
      processors: [resourcedetection/entity, transform/entity-event, batch]
      exporters: [coralogix/resource_catalog]

    metrics/otel-collector:
      receivers: [prometheus, hostmetrics]
      processors: [resourcedetection/otel-collector, resource/metadata, transform/prometheus, batch]
      exporters: [coralogix]

  telemetry:
    logs:
      level: warn
    metrics:
      readers:
        - pull:
            exporter:
              prometheus:
                host: 0.0.0.0
                port: 8888
