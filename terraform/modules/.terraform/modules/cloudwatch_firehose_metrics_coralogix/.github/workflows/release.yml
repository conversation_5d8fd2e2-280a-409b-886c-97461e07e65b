name: release

on:
  workflow_dispatch:
  push:
    branches:
      - main
      - master
    paths:
      - '**/*.tpl'
      - '**/*.py'
      - '**/*.tf'
      - '.github/workflows/release.yml'

jobs:
  release:
    name: release
    runs-on: ubuntu-latest
    if: github.repository_owner == 'coralogix'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: false
          fetch-depth: 0

      - name: Release
        uses: cycjimmy/semantic-release-action@v3.4.2
        with:
          semantic_version: 19.0.5
          extra_plugins: |
            conventional-changelog-conventionalcommits@4.6.3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
