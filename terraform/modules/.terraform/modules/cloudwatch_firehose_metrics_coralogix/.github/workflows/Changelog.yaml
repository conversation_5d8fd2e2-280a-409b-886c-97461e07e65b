name: Changelog
on:
  pull_request:
    types: [opened, synchronize, labeled]
    branches: [master]
    paths:
      - "modules/**"

jobs:
  get-label:
    runs-on: ubuntu-latest
    name: get label
    outputs:
      labels: "${{ steps.pr-labels.outputs.labels }}"
    steps:
      - name: Get PR labels
        id: pr-labels
        uses: joerick/pr-labels-action@v1.0.9
        
  check-changelog-updates:
    if: "${{ needs.get-label.outputs.labels != ' skip-changelog ' }}"
    runs-on: ubuntu-latest
    needs: get-label
    name: Check changelog update
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Get changed files
      id: changed-files
      uses: tj-actions/changed-files@v46.0.1

    - name: Move integrations files 
      run: |
        changed_files_string="${{ steps.changed-files.outputs.all_changed_files }}"
        read -ra changed_files_array <<< "$changed_files_string"
        chmod +x changelog_check.sh
        for changed_file in "${changed_files_array[@]}"; do
          if [[ "$changed_file" == *"/main.tf" ]] && [[ "$changed_file" != "examples"* ]]; then
            ./changelog_check.sh
          fi
        done
