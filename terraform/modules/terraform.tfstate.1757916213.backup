{"version": 4, "terraform_version": "1.9.5", "serial": 27, "lineage": "4a6d921f-16f6-0cad-2d2e-be423f2f7043", "outputs": {}, "resources": [{"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "data", "type": "aws_caller_identity", "name": "current_identity", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:iam::************:user/cxthulasi", "id": "************", "user_id": "AIDAYWBFNAMSAFONOAKQF"}, "sensitive_attributes": []}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "data", "type": "aws_iam_policy_document", "name": "lambda_assume_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"sts:AssumeRole\",\n      \"Principal\": {\n        \"Service\": \"lambda.amazonaws.com\"\n      }\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":\"sts:AssumeRole\",\"Principal\":{\"Service\":\"lambda.amazonaws.com\"}}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["sts:<PERSON><PERSON>Role"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["lambda.amazonaws.com"], "type": "Service"}], "resources": [], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "data", "type": "aws_region", "name": "current_region", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"description": "Asia Pacific (Mumbai)", "endpoint": "ec2.ap-south-1.amazonaws.com", "id": "ap-south-1", "name": "ap-south-1"}, "sensitive_attributes": []}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_cloudwatch_log_group", "name": "firehose_loggroup", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:ap-south-1:************:log-group:/aws/kinesisfirehose/cx-axis-log-testing", "id": "/aws/kinesisfirehose/cx-axis-log-testing", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/aws/kinesisfirehose/cx-axis-log-testing", "name_prefix": "", "retention_in_days": 1, "skip_destroy": false, "tags": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "tags_all": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_cloudwatch_log_group", "name": "loggroup", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:logs:ap-south-1:************:log-group:/aws/lambda/cx-axis-log-testing-metrics-transform", "id": "/aws/lambda/cx-axis-log-testing-metrics-transform", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/aws/lambda/cx-axis-log-testing-metrics-transform", "name_prefix": "", "retention_in_days": 1, "skip_destroy": false, "tags": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "tags_all": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.cloudwatch_firehose_metrics_coralogix.aws_iam_role.lambda_iam_role", "module.cloudwatch_firehose_metrics_coralogix.aws_lambda_function.lambda_processor", "module.cloudwatch_firehose_metrics_coralogix.data.aws_iam_policy_document.lambda_assume_role", "module.cloudwatch_firehose_metrics_coralogix.data.aws_region.current_region"]}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_cloudwatch_log_stream", "name": "firehose_logstream_backup", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:ap-south-1:************:log-group:/aws/kinesisfirehose/cx-axis-log-testing:log-stream:BackupDelivery", "id": "BackupDelivery", "log_group_name": "/aws/kinesisfirehose/cx-axis-log-testing", "name": "BackupDelivery"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.cloudwatch_firehose_metrics_coralogix.aws_cloudwatch_log_group.firehose_loggroup"]}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_cloudwatch_log_stream", "name": "firehose_logstream_dest", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:ap-south-1:************:log-group:/aws/kinesisfirehose/cx-axis-log-testing:log-stream:DestinationDelivery", "id": "DestinationDelivery", "log_group_name": "/aws/kinesisfirehose/cx-axis-log-testing", "name": "DestinationDelivery"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.cloudwatch_firehose_metrics_coralogix.aws_cloudwatch_log_group.firehose_loggroup"]}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_cloudwatch_metric_stream", "name": "cloudwatch_metric_stream", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:cloudwatch:ap-south-1:************:metric-stream/cx-axis-log-testing", "creation_date": "2025-09-15T06:00:19Z", "exclude_filter": [], "firehose_arn": "arn:aws:firehose:ap-south-1:************:deliverystream/cx-axis-log-testing-metrics", "id": "cx-axis-log-testing", "include_filter": [], "include_linked_accounts_metrics": false, "last_update_date": "2025-09-15T06:00:19Z", "name": "cx-axis-log-testing", "name_prefix": "", "output_format": "opentelemetry0.7", "role_arn": "arn:aws:iam::************:role/cx-axis-log-testing-cw", "state": "running", "statistics_configuration": [], "tags": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "tags_all": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "timeouts": null}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************", "dependencies": ["module.cloudwatch_firehose_metrics_coralogix.aws_cloudwatch_log_group.firehose_loggroup", "module.cloudwatch_firehose_metrics_coralogix.aws_cloudwatch_log_stream.firehose_logstream_dest", "module.cloudwatch_firehose_metrics_coralogix.aws_iam_role.firehose_to_coralogix", "module.cloudwatch_firehose_metrics_coralogix.aws_iam_role.lambda_iam_role", "module.cloudwatch_firehose_metrics_coralogix.aws_iam_role.metric_streams_to_firehose_role", "module.cloudwatch_firehose_metrics_coralogix.aws_kinesis_firehose_delivery_stream.coralogix_stream_metrics", "module.cloudwatch_firehose_metrics_coralogix.aws_lambda_function.lambda_processor", "module.cloudwatch_firehose_metrics_coralogix.aws_s3_bucket.firehose_bucket", "module.cloudwatch_firehose_metrics_coralogix.data.aws_caller_identity.current_identity", "module.cloudwatch_firehose_metrics_coralogix.data.aws_iam_policy_document.lambda_assume_role", "module.cloudwatch_firehose_metrics_coralogix.data.aws_region.current_region"]}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_iam_policy", "name": "firehose_to_coralogix_metric_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:policy/cx-axis-log-testing-metrics-policy", "attachment_count": 1, "description": "", "id": "arn:aws:iam::************:policy/cx-axis-log-testing-metrics-policy", "name": "cx-axis-log-testing-metrics-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"s3:AbortMultipartUpload\",\"s3:GetBucketLocation\",\"s3:GetObject\",\"s3:ListBucket\",\"s3:ListBucketMultipartUploads\",\"s3:PutObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::cx-axis-log-testing-backup\",\"arn:aws:s3:::cx-axis-log-testing-backup/*\"]},{\"Action\":[\"kms:Decrypt\",\"kms:GenerateDataKey\"],\"Condition\":{\"StringEquals\":{\"kms:ViaService\":\"s3.ap-south-1.amazonaws.com\"},\"StringLike\":{\"kms:EncryptionContext:aws:s3:arn\":\"arn:aws:s3:::cx-axis-log-testing-backup/prefix*\"}},\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:kms:ap-south-1:************:key/key-id\"]},{\"Action\":[\"kinesis:DescribeStream\",\"kinesis:GetShardIterator\",\"kinesis:GetRecords\",\"kinesis:ListShards\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:kinesis:ap-south-1:************:stream/*\"},{\"Action\":[\"logs:PutLogEvents\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:logs:ap-south-1:************:log-group:/aws/kinesisfirehose/cx-axis-log-testing\"]},{\"Action\":[\"lambda:InvokeFunction\",\"lambda:GetFunctionConfiguration\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:lambda:ap-south-1:************:function:cx-axis-log-testing-metrics-transform:*\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAYWBFNAMSHGEZBUVRM", "tags": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "tags_all": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.cloudwatch_firehose_metrics_coralogix.aws_cloudwatch_log_group.firehose_loggroup", "module.cloudwatch_firehose_metrics_coralogix.aws_iam_role.lambda_iam_role", "module.cloudwatch_firehose_metrics_coralogix.aws_lambda_function.lambda_processor", "module.cloudwatch_firehose_metrics_coralogix.aws_s3_bucket.firehose_bucket", "module.cloudwatch_firehose_metrics_coralogix.data.aws_caller_identity.current_identity", "module.cloudwatch_firehose_metrics_coralogix.data.aws_iam_policy_document.lambda_assume_role", "module.cloudwatch_firehose_metrics_coralogix.data.aws_region.current_region"]}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_iam_role", "name": "firehose_to_coralogix", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/cx-axis-log-testing-firehose", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeR<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"firehose.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-09-15T05:59:43Z", "description": "", "force_detach_policies": false, "id": "cx-axis-log-testing-firehose", "inline_policy": [{"name": "cx-axis-log-testing-firehose", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"s3:AbortMultipartUpload\",\"s3:GetBucketLocation\",\"s3:GetObject\",\"s3:ListBucket\",\"s3:ListBucketMultipartUploads\",\"s3:PutObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::cx-axis-log-testing-backup\",\"arn:aws:s3:::cx-axis-log-testing-backup/*\"]},{\"Action\":[\"kinesis:DescribeStream\",\"kinesis:GetShardIterator\",\"kinesis:GetRecords\",\"kinesis:ListShards\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:kinesis:ap-south-1:************:stream/*\"},{\"Action\":[\"*\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:logs:ap-south-1:************:log-group:/aws/kinesisfirehose/cx-axis-log-testing\"]}]}"}], "managed_policy_arns": ["arn:aws:iam::************:policy/cx-axis-log-testing-metrics-policy"], "max_session_duration": 3600, "name": "cx-axis-log-testing-firehose", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "tags_all": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "unique_id": "AROAYWBFNAMSACO42FDK7"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.cloudwatch_firehose_metrics_coralogix.aws_cloudwatch_log_group.firehose_loggroup", "module.cloudwatch_firehose_metrics_coralogix.aws_s3_bucket.firehose_bucket", "module.cloudwatch_firehose_metrics_coralogix.data.aws_caller_identity.current_identity", "module.cloudwatch_firehose_metrics_coralogix.data.aws_region.current_region"]}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_iam_role", "name": "lambda_iam_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/cx-axis-log-testing-metrics-transform-lambda", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeR<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"lambda.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-09-15T05:51:33Z", "description": "", "force_detach_policies": false, "id": "cx-axis-log-testing-metrics-transform-lambda", "inline_policy": [{"name": "cx-axis-log-testing-metrics-transform-lambda", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"tag:GetResources\",\"cloudwatch:GetMetricData\",\"cloudwatch:GetMetricStatistics\",\"cloudwatch:ListMetrics\",\"apigateway:GET\",\"aps:ListWorkspaces\",\"autoscaling:DescribeAutoScalingGroups\",\"dms:DescribeReplicationInstances\",\"dms:DescribeReplicationTasks\",\"ec2:DescribeTransitGatewayAttachments\",\"ec2:DescribeSpotFleetRequests\",\"storagegateway:ListGateways\",\"storagegateway:ListTagsForResource\"],\"Effect\":\"Allow\",\"Resource\":\"*\",\"Sid\":\"\"},{\"Action\":[\"logs:PutLogEvents\",\"logs:CreateLogStream\",\"logs:CreateLogGroup\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:logs:*:*:*\",\"Sid\":\"\"}]}"}], "managed_policy_arns": [], "max_session_duration": 3600, "name": "cx-axis-log-testing-metrics-transform-lambda", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "tags_all": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "unique_id": "AROAYWBFNAMSMIUPS564A"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.cloudwatch_firehose_metrics_coralogix.data.aws_iam_policy_document.lambda_assume_role"]}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_iam_role", "name": "metric_streams_to_firehose_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/cx-axis-log-testing-cw", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"streams.metrics.cloudwatch.amazonaws.com\"},\"Sid\":\"\"}],\"Version\":\"2012-10-17\"}", "create_date": "2025-09-15T05:51:33Z", "description": "", "force_detach_policies": false, "id": "cx-axis-log-testing-cw", "inline_policy": [{"name": "cx-axis-log-testing-cw", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"firehose:DeleteDeliveryStream\",\"firehose:PutRecord\",\"firehose:PutRecordBatch\",\"firehose:UpdateDestination\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:firehose:ap-south-1:************:deliverystream/cx-axis-log-testing-metrics\"}]}"}], "managed_policy_arns": [], "max_session_duration": 3600, "name": "cx-axis-log-testing-cw", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "tags_all": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "unique_id": "AROAYWBFNAMSENGGWVAFM"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_iam_role_policy", "name": "lambda_iam_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"id": "cx-axis-log-testing-metrics-transform-lambda:cx-axis-log-testing-metrics-transform-lambda", "name": "cx-axis-log-testing-metrics-transform-lambda", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"tag:GetResources\",\"cloudwatch:GetMetricData\",\"cloudwatch:GetMetricStatistics\",\"cloudwatch:ListMetrics\",\"apigateway:GET\",\"aps:ListWorkspaces\",\"autoscaling:DescribeAutoScalingGroups\",\"dms:DescribeReplicationInstances\",\"dms:DescribeReplicationTasks\",\"ec2:DescribeTransitGatewayAttachments\",\"ec2:DescribeSpotFleetRequests\",\"storagegateway:ListGateways\",\"storagegateway:ListTagsForResource\"],\"Effect\":\"Allow\",\"Resource\":\"*\",\"Sid\":\"\"},{\"Action\":[\"logs:PutLogEvents\",\"logs:CreateLogStream\",\"logs:CreateLogGroup\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:logs:*:*:*\",\"Sid\":\"\"}]}", "role": "cx-axis-log-testing-metrics-transform-lambda"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.cloudwatch_firehose_metrics_coralogix.aws_iam_role.lambda_iam_role", "module.cloudwatch_firehose_metrics_coralogix.data.aws_iam_policy_document.lambda_assume_role"]}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_iam_role_policy", "name": "metric_streams_to_firehose_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"id": "cx-axis-log-testing-cw:cx-axis-log-testing-cw", "name": "cx-axis-log-testing-cw", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"firehose:DeleteDeliveryStream\",\"firehose:PutRecord\",\"firehose:PutRecordBatch\",\"firehose:UpdateDestination\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:firehose:ap-south-1:************:deliverystream/cx-axis-log-testing-metrics\"}]}", "role": "cx-axis-log-testing-cw"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.cloudwatch_firehose_metrics_coralogix.aws_cloudwatch_log_group.firehose_loggroup", "module.cloudwatch_firehose_metrics_coralogix.aws_cloudwatch_log_stream.firehose_logstream_dest", "module.cloudwatch_firehose_metrics_coralogix.aws_iam_role.firehose_to_coralogix", "module.cloudwatch_firehose_metrics_coralogix.aws_iam_role.lambda_iam_role", "module.cloudwatch_firehose_metrics_coralogix.aws_iam_role.metric_streams_to_firehose_role", "module.cloudwatch_firehose_metrics_coralogix.aws_kinesis_firehose_delivery_stream.coralogix_stream_metrics", "module.cloudwatch_firehose_metrics_coralogix.aws_lambda_function.lambda_processor", "module.cloudwatch_firehose_metrics_coralogix.aws_s3_bucket.firehose_bucket", "module.cloudwatch_firehose_metrics_coralogix.data.aws_caller_identity.current_identity", "module.cloudwatch_firehose_metrics_coralogix.data.aws_iam_policy_document.lambda_assume_role", "module.cloudwatch_firehose_metrics_coralogix.data.aws_region.current_region"]}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "firehose_to_coralogix_metric_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"id": "cx-axis-log-testing-firehose-20250915055947759700000001", "policy_arn": "arn:aws:iam::************:policy/cx-axis-log-testing-metrics-policy", "role": "cx-axis-log-testing-firehose"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.cloudwatch_firehose_metrics_coralogix.aws_cloudwatch_log_group.firehose_loggroup", "module.cloudwatch_firehose_metrics_coralogix.aws_iam_policy.firehose_to_coralogix_metric_policy", "module.cloudwatch_firehose_metrics_coralogix.aws_iam_role.firehose_to_coralogix", "module.cloudwatch_firehose_metrics_coralogix.aws_iam_role.lambda_iam_role", "module.cloudwatch_firehose_metrics_coralogix.aws_lambda_function.lambda_processor", "module.cloudwatch_firehose_metrics_coralogix.aws_s3_bucket.firehose_bucket", "module.cloudwatch_firehose_metrics_coralogix.data.aws_caller_identity.current_identity", "module.cloudwatch_firehose_metrics_coralogix.data.aws_iam_policy_document.lambda_assume_role", "module.cloudwatch_firehose_metrics_coralogix.data.aws_region.current_region"]}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_kinesis_firehose_delivery_stream", "name": "coralogix_stream_metrics", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:firehose:ap-south-1:************:deliverystream/cx-axis-log-testing-metrics", "destination": "http_endpoint", "destination_id": "destinationId-000000000001", "elasticsearch_configuration": [], "extended_s3_configuration": [], "http_endpoint_configuration": [{"access_key": "cxtp_Qd7ke2dhCmpybJuGZxBn5EYeuXUjlT", "buffering_interval": 60, "buffering_size": 1, "cloudwatch_logging_options": [{"enabled": true, "log_group_name": "/aws/kinesisfirehose/cx-axis-log-testing", "log_stream_name": "DestinationDelivery"}], "name": "Coralogix", "processing_configuration": [{"enabled": true, "processors": [{"parameters": [{"parameter_name": "BufferIntervalInSeconds", "parameter_value": "61"}, {"parameter_name": "BufferSizeInMBs", "parameter_value": "0.2"}, {"parameter_name": "LambdaArn", "parameter_value": "arn:aws:lambda:ap-south-1:************:function:cx-axis-log-testing-metrics-transform:$LATEST"}], "type": "Lambda"}]}], "request_configuration": [{"common_attributes": [{"name": "integrationType", "value": "CloudWatch_Metrics_OpenTelemetry070"}], "content_encoding": "GZIP"}], "retry_duration": 30, "role_arn": "arn:aws:iam::************:role/cx-axis-log-testing-firehose", "s3_backup_mode": "FailedDataOnly", "s3_configuration": [{"bucket_arn": "arn:aws:s3:::cx-axis-log-testing-backup", "buffering_interval": 300, "buffering_size": 5, "cloudwatch_logging_options": [{"enabled": false, "log_group_name": "", "log_stream_name": ""}], "compression_format": "GZIP", "error_output_prefix": "", "kms_key_arn": "", "prefix": "", "role_arn": "arn:aws:iam::************:role/cx-axis-log-testing-firehose"}], "secrets_manager_configuration": [], "url": "https://firehose-ingress.coralogix.in/firehose"}], "iceberg_configuration": [], "id": "arn:aws:firehose:ap-south-1:************:deliverystream/cx-axis-log-testing-metrics", "kinesis_source_configuration": [], "msk_source_configuration": [], "name": "cx-axis-log-testing-metrics", "opensearch_configuration": [], "opensearchserverless_configuration": [], "redshift_configuration": [], "server_side_encryption": [{"enabled": false, "key_arn": "", "key_type": "AWS_OWNED_CMK"}], "snowflake_configuration": [], "splunk_configuration": [], "tags": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "tags_all": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "timeouts": null, "version_id": "1"}, "sensitive_attributes": [[{"type": "get_attr", "value": "http_endpoint_configuration"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "access_key"}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInVwZGF0ZSI6NjAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["module.cloudwatch_firehose_metrics_coralogix.aws_cloudwatch_log_group.firehose_loggroup", "module.cloudwatch_firehose_metrics_coralogix.aws_cloudwatch_log_stream.firehose_logstream_dest", "module.cloudwatch_firehose_metrics_coralogix.aws_iam_role.firehose_to_coralogix", "module.cloudwatch_firehose_metrics_coralogix.aws_iam_role.lambda_iam_role", "module.cloudwatch_firehose_metrics_coralogix.aws_lambda_function.lambda_processor", "module.cloudwatch_firehose_metrics_coralogix.aws_s3_bucket.firehose_bucket", "module.cloudwatch_firehose_metrics_coralogix.data.aws_caller_identity.current_identity", "module.cloudwatch_firehose_metrics_coralogix.data.aws_iam_policy_document.lambda_assume_role", "module.cloudwatch_firehose_metrics_coralogix.data.aws_region.current_region"]}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_lambda_function", "name": "lambda_processor", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"architectures": ["arm64"], "arn": "arn:aws:lambda:ap-south-1:************:function:cx-axis-log-testing-metrics-transform", "code_sha256": "MT64zPFWDL1fU6BSJXvhvSEtV3iymOwJuBJ5FdXIyJg=", "code_signing_config_arn": "", "dead_letter_config": [], "description": "", "environment": [{"variables": {"FILE_CACHE_PATH": "/tmp"}}], "ephemeral_storage": [{"size": 512}], "file_system_config": [], "filename": null, "function_name": "cx-axis-log-testing-metrics-transform", "handler": "bootstrap", "id": "cx-axis-log-testing-metrics-transform", "image_config": [], "image_uri": "", "invoke_arn": "arn:aws:apigateway:ap-south-1:lambda:path/2015-03-31/functions/arn:aws:lambda:ap-south-1:************:function:cx-axis-log-testing-metrics-transform/invocations", "kms_key_arn": "", "last_modified": "2025-09-15T05:59:40.539+0000", "layers": [], "logging_config": [{"application_log_level": "", "log_format": "Text", "log_group": "/aws/lambda/cx-axis-log-testing-metrics-transform", "system_log_level": ""}], "memory_size": 512, "package_type": "Zip", "publish": false, "qualified_arn": "arn:aws:lambda:ap-south-1:************:function:cx-axis-log-testing-metrics-transform:$LATEST", "qualified_invoke_arn": "arn:aws:apigateway:ap-south-1:lambda:path/2015-03-31/functions/arn:aws:lambda:ap-south-1:************:function:cx-axis-log-testing-metrics-transform:$LATEST/invocations", "replace_security_groups_on_destroy": null, "replacement_security_group_ids": null, "reserved_concurrent_executions": -1, "role": "arn:aws:iam::************:role/cx-axis-log-testing-metrics-transform-lambda", "runtime": "provided.al2", "s3_bucket": "cx-cw-metrics-tags-lambda-processor-ap-south-1", "s3_key": "bootstrap.zip", "s3_object_version": null, "signing_job_arn": "", "signing_profile_version_arn": "", "skip_destroy": false, "snap_start": [], "source_code_hash": "", "source_code_size": 12025356, "tags": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "tags_all": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "timeout": 60, "timeouts": null, "tracing_config": [{"mode": "PassThrough"}], "version": "$LATEST", "vpc_config": []}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["module.cloudwatch_firehose_metrics_coralogix.aws_iam_role.lambda_iam_role", "module.cloudwatch_firehose_metrics_coralogix.data.aws_iam_policy_document.lambda_assume_role", "module.cloudwatch_firehose_metrics_coralogix.data.aws_region.current_region"]}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_s3_bucket", "name": "firehose_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::cx-axis-log-testing-backup", "bucket": "cx-axis-log-testing-backup", "bucket_domain_name": "cx-axis-log-testing-backup.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "cx-axis-log-testing-backup.s3.ap-south-1.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "9a10bfe95c3a7efdb6ebc56f797f90007236c16e851616eb11d374d670078f51", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z11RGJOFQNVJUP", "id": "cx-axis-log-testing-backup", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "", "region": "ap-south-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {"Name": "cx-axis-log-testing-backup", "custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "tags_all": {"Name": "cx-axis-log-testing-backup", "custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_s3_bucket_public_access_block", "name": "firehose_bucket_bucket_access", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"block_public_acls": true, "block_public_policy": true, "bucket": "cx-axis-log-testing-backup", "id": "cx-axis-log-testing-backup", "ignore_public_acls": true, "restrict_public_buckets": true}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.cloudwatch_firehose_metrics_coralogix.aws_s3_bucket.firehose_bucket"]}]}], "check_results": [{"object_kind": "var", "config_addr": "module.cloudwatch_firehose_metrics_coralogix.var.coralogix_region", "status": "unknown", "objects": [{"object_addr": "module.cloudwatch_firehose_metrics_coralogix.var.coralogix_region", "status": "unknown"}]}]}