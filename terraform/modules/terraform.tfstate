{"version": 4, "terraform_version": "1.9.5", "serial": 49, "lineage": "4a6d921f-16f6-0cad-2d2e-be423f2f7043", "outputs": {}, "resources": [{"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "data", "type": "aws_caller_identity", "name": "current_identity", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:iam::************:user/cxthulasi", "id": "************", "user_id": "AIDAYWBFNAMSAFONOAKQF"}, "sensitive_attributes": []}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "data", "type": "aws_iam_policy_document", "name": "assume_new_firehose_iam", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "*********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"sts:AssumeR<PERSON>\",\n      \"Principal\": {\n        \"Service\": \"firehose.amazonaws.com\"\n      }\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":\"sts:AssumeR<PERSON>\",\"Principal\":{\"Service\":\"firehose.amazonaws.com\"}}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["sts:<PERSON><PERSON>Role"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["firehose.amazonaws.com"], "type": "Service"}], "resources": [], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "data", "type": "aws_iam_policy_document", "name": "lambda_assume_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"sts:AssumeRole\",\n      \"Principal\": {\n        \"Service\": \"lambda.amazonaws.com\"\n      }\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":\"sts:AssumeRole\",\"Principal\":{\"Service\":\"lambda.amazonaws.com\"}}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["sts:<PERSON><PERSON>Role"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["lambda.amazonaws.com"], "type": "Service"}], "resources": [], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "data", "type": "aws_iam_role", "name": "existing_firehose_iam", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "data", "type": "aws_iam_role", "name": "existing_lambda_iam", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "data", "type": "aws_iam_role", "name": "existing_metric_streams_iam", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "data", "type": "aws_partition", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"dns_suffix": "amazonaws.com", "id": "aws", "partition": "aws", "reverse_dns_prefix": "com.amazonaws"}, "sensitive_attributes": []}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "data", "type": "aws_region", "name": "current_region", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"description": "Asia Pacific (Mumbai)", "endpoint": "ec2.ap-south-1.amazonaws.com", "id": "ap-south-1", "name": "ap-south-1"}, "sensitive_attributes": []}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "data", "type": "aws_s3_bucket", "name": "exisiting_s3_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:s3:::cx-axis-log-testing-backup", "bucket": "cx-axis-log-testing-backup", "bucket_domain_name": "cx-axis-log-testing-backup.s3.amazonaws.com", "bucket_regional_domain_name": "cx-axis-log-testing-backup.s3.ap-south-1.amazonaws.com", "hosted_zone_id": "Z11RGJOFQNVJUP", "id": "cx-axis-log-testing-backup", "region": "ap-south-1", "website_domain": null, "website_endpoint": null}, "sensitive_attributes": []}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_cloudwatch_log_group", "name": "firehose_loggroup", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:ap-south-1:************:log-group:/aws/kinesisfirehosemetrics/cx-axis-log-testing", "id": "/aws/kinesisfirehosemetrics/cx-axis-log-testing", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/aws/kinesisfirehosemetrics/cx-axis-log-testing", "name_prefix": "", "retention_in_days": 1, "skip_destroy": false, "tags": {"custom_endpoint": "https://ingress.ap1.coralogix.com/aws/firehose", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "tags_all": {"custom_endpoint": "https://ingress.ap1.coralogix.com/aws/firehose", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_cloudwatch_log_group", "name": "loggroup", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:logs:ap-south-1:************:log-group:/aws/lambda/cx-axis-log-testing-metrics-transform", "id": "/aws/lambda/cx-axis-log-testing-metrics-transform", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/aws/lambda/cx-axis-log-testing-metrics-transform", "name_prefix": "", "retention_in_days": 1, "skip_destroy": false, "tags": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "tags_all": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.cloudwatch_firehose_metrics_coralogix.aws_iam_role.lambda_iam_role", "module.cloudwatch_firehose_metrics_coralogix.aws_iam_role.new_lambda_iam", "module.cloudwatch_firehose_metrics_coralogix.aws_lambda_function.lambda_processor", "module.cloudwatch_firehose_metrics_coralogix.data.aws_iam_policy_document.lambda_assume_policy", "module.cloudwatch_firehose_metrics_coralogix.data.aws_iam_policy_document.lambda_assume_role", "module.cloudwatch_firehose_metrics_coralogix.data.aws_iam_role.existing_lambda_iam", "module.cloudwatch_firehose_metrics_coralogix.data.aws_region.current_region", "module.cloudwatch_firehose_metrics_coralogix.null_resource.s3_bucket_copy"]}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_cloudwatch_log_stream", "name": "firehose_logstream_backup", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:ap-south-1:************:log-group:/aws/kinesisfirehosemetrics/cx-axis-log-testing:log-stream:BackupDelivery", "id": "BackupDelivery", "log_group_name": "/aws/kinesisfirehosemetrics/cx-axis-log-testing", "name": "BackupDelivery"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.cloudwatch_firehose_metrics_coralogix.aws_cloudwatch_log_group.firehose_loggroup"]}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_cloudwatch_log_stream", "name": "firehose_logstream_dest", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:ap-south-1:************:log-group:/aws/kinesisfirehosemetrics/cx-axis-log-testing:log-stream:DestinationDelivery", "id": "DestinationDelivery", "log_group_name": "/aws/kinesisfirehosemetrics/cx-axis-log-testing", "name": "DestinationDelivery"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.cloudwatch_firehose_metrics_coralogix.aws_cloudwatch_log_group.firehose_loggroup"]}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_lambda_function", "name": "lambda_processor", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"architectures": ["arm64"], "arn": "arn:aws:lambda:ap-south-1:************:function:cx-axis-log-testing-metrics-transform", "code_sha256": "MT64zPFWDL1fU6BSJXvhvSEtV3iymOwJuBJ5FdXIyJg=", "code_signing_config_arn": "", "dead_letter_config": [], "description": "", "environment": [{"variables": {"FILE_CACHE_PATH": "/tmp"}}], "ephemeral_storage": [{"size": 512}], "file_system_config": [], "filename": null, "function_name": "cx-axis-log-testing-metrics-transform", "handler": "bootstrap", "id": "cx-axis-log-testing-metrics-transform", "image_config": [], "image_uri": "", "invoke_arn": "arn:aws:apigateway:ap-south-1:lambda:path/2015-03-31/functions/arn:aws:lambda:ap-south-1:************:function:cx-axis-log-testing-metrics-transform/invocations", "kms_key_arn": "", "last_modified": "2025-09-15T05:59:40.539+0000", "layers": [], "logging_config": [{"application_log_level": "", "log_format": "Text", "log_group": "/aws/lambda/cx-axis-log-testing-metrics-transform", "system_log_level": ""}], "memory_size": 512, "package_type": "Zip", "publish": false, "qualified_arn": "arn:aws:lambda:ap-south-1:************:function:cx-axis-log-testing-metrics-transform:$LATEST", "qualified_invoke_arn": "arn:aws:apigateway:ap-south-1:lambda:path/2015-03-31/functions/arn:aws:lambda:ap-south-1:************:function:cx-axis-log-testing-metrics-transform:$LATEST/invocations", "replace_security_groups_on_destroy": null, "replacement_security_group_ids": null, "reserved_concurrent_executions": -1, "role": "arn:aws:iam::************:role/cx-axis-log-testing-metrics-transform-lambda", "runtime": "provided.al2", "s3_bucket": "cx-cw-metrics-tags-lambda-processor-ap-south-1", "s3_key": "bootstrap.zip", "s3_object_version": null, "signing_job_arn": "", "signing_profile_version_arn": "", "skip_destroy": false, "snap_start": [], "source_code_hash": "", "source_code_size": 12025356, "tags": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "tags_all": {"custom_endpoint": "_default_", "managed-by": "coralogix-terraform", "terraform-module": "kinesis-firehose-to-coralogix", "terraform-module-version": "v0.1.0"}, "timeout": 60, "timeouts": null, "tracing_config": [{"mode": "PassThrough"}], "version": "$LATEST", "vpc_config": []}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["module.cloudwatch_firehose_metrics_coralogix.aws_iam_role.lambda_iam_role", "module.cloudwatch_firehose_metrics_coralogix.aws_iam_role.new_lambda_iam", "module.cloudwatch_firehose_metrics_coralogix.data.aws_iam_policy_document.lambda_assume_policy", "module.cloudwatch_firehose_metrics_coralogix.data.aws_iam_policy_document.lambda_assume_role", "module.cloudwatch_firehose_metrics_coralogix.data.aws_iam_role.existing_lambda_iam", "module.cloudwatch_firehose_metrics_coralogix.data.aws_region.current_region", "module.cloudwatch_firehose_metrics_coralogix.null_resource.s3_bucket_copy"]}]}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "aws_s3_bucket", "name": "new_s3_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "null_resource", "name": "s3_bucket_copy", "provider": "provider[\"registry.terraform.io/hashicorp/null\"]", "instances": []}, {"module": "module.cloudwatch_firehose_metrics_coralogix", "mode": "managed", "type": "random_string", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"schema_version": 2, "attributes": {"id": "dn23zt", "keepers": null, "length": 6, "lower": true, "min_lower": 0, "min_numeric": 0, "min_special": 0, "min_upper": 0, "number": true, "numeric": true, "override_special": null, "result": "dn23zt", "special": false, "upper": false}, "sensitive_attributes": []}]}], "check_results": [{"object_kind": "var", "config_addr": "module.cloudwatch_firehose_metrics_coralogix.var.server_side_encryption", "status": "pass", "objects": [{"object_addr": "module.cloudwatch_firehose_metrics_coralogix.var.server_side_encryption", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.cloudwatch_firehose_metrics_coralogix.var.coralogix_region", "status": "pass", "objects": [{"object_addr": "module.cloudwatch_firehose_metrics_coralogix.var.coralogix_region", "status": "pass"}]}]}