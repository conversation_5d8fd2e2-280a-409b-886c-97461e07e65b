#!/bin/bash

# Parameters
OTEL_VERSION="0.135.0"
CONFIG_FILE="/etc/otelcol-contrib/config.yaml"

wget https://github.com/open-telemetry/opentelemetry-collector-releases/releases/download/v${OTEL_VERSION}/otelcol-contrib_${OTEL_VERSION}_linux_amd64.deb
sudo dpkg -i otelcol-contrib_${OTEL_VERSION}_linux_amd64.deb

# Create a basic config file
cat << EOF | sudo tee $CONFIG_FILE
receivers:
  otlp:
    protocols:
      grpc: { endpoint: 0.0.0.0:4317 }
      http: { endpoint: 0.0.0.0:4318 }
  filelog:
    force_flush_period: 0
    include:
    - /var/log/syslog
    - /var/log/more/examples/*/*.log
    include_file_name: true
    include_file_path: false
    retry_on_failure:
      enabled: true
    start_at: beginning
    storage: file_storage
  hostmetrics:
    root_path: /
    collection_interval: 10s
    scrapers:
      cpu: { metrics: { system.cpu.utilization: { enabled: true } } }
      filesystem:
        exclude_fs_types: { fs_types: [autofs, binfmt_misc, bpf, cgroup2, configfs, debugfs, devpts, devtmpfs, fusectl, hugetlbfs, iso9660, mqueue, nsfs, overlay, proc, procfs, pstore, rpc_pipefs, securityfs, selinuxfs, squashfs, sysfs, tracefs], match_type: strict }
        exclude_mount_points: { match_type: regexp, mount_points: [/dev/*, /proc/*, /sys/*, /run/k3s/containerd/*, /run/containerd/runc/*, /var/lib/docker/*, /var/lib/kubelet/*, /snap/*] }
      memory: { metrics: { system.memory.utilization: { enabled: true } } }
      network:
      process:
        metrics:
          process.cpu.utilization: { enabled: true }
          process.memory.utilization: { enabled: true }
          process.threads: { enabled: true }
        mute_process_exe_error: true
        mute_process_user_error: true
connectors:
  forward/sampled: {}
  spanmetrics:
    namespace: ""
    histogram:
      explicit:
        buckets: [100us, 1ms, 2ms, 4ms, 6ms, 10ms, 100ms, 250ms]
    dimensions:
      - name: http.method
      - name: cgx.transaction
      - name: cgx.transaction.root
    exemplars:
      enabled: true
    aggregation_cardinality_limit: 1000
    aggregation_temporality: "AGGREGATION_TEMPORALITY_CUMULATIVE"
    metrics_flush_interval: 15s
    events:
      enabled: true
      dimensions:
        - name: exception.type
        - name: exception.message

processors:
  batch: { send_batch_max_size: 2048, send_batch_size: 1024, timeout: 1s }
  resourcedetection:
    detectors: [system, env, ec2]
    override: false
    timeout: 2s
  resource/metadata:
    attributes:
      - action: upsert
        key: cx.otel_integration.name
        value: coralogix-integration-OTEL-EC2-service
  resourcedetection/entity:
    detectors: [system, env, ec2, ecs]
    override: false
    timeout: 2s
    system:
      resource_attributes:
        host.id: { enabled: false }
        host.cpu.cache.l2.size: { enabled: true }
        host.cpu.stepping: { enabled: true }
        host.cpu.model.name: { enabled: true }
        host.cpu.model.id: { enabled: true }
        host.cpu.family: { enabled: true }
        host.cpu.vendor.id: { enabled: true }
        host.mac: { enabled: true }
        host.ip: { enabled: true }
        os.description: { enabled: true }
  transform/entity-event:
    error_mode: silent
    log_statements:
      - context: log
        statements:
          - set(attributes["otel.entity.id"]["host.id"], resource.attributes["host.id"])
          - merge_maps(attributes, resource.attributes, "insert")
      - context: resource
        statements:
          - keep_keys(attributes, [""])

exporters:
  coralogix:
    domain: "coralogix.in"
    private_key: "cxtp_Qd7ke2dhCmpybJuGZxBn5EYeuXUjlT"
    application_name: "ec2oteltraces"
    subsystem_name: "ec2-subsystem"
    # These configuration values can be used to dynamically set the application_name and subsystem_name
    # application_name_attributes:
    # - host.id
    # subsystem_name_attributes: 
    # - host.name
    timeout: 30s
  coralogix/resource_catalog:
    application_name: resource
    domain: "coralogix.in"
    private_key: "cxtp_Qd7ke2dhCmpybJuGZxBn5EYeuXUjlT"
    logs:
      headers:
        X-Coralogix-Distribution: ec2-local-integration/0.0.1
        x-coralogix-ingress: metadata-as-otlp-logs/v1
    subsystem_name: catalog
    timeout: 30s

extensions:
  file_storage: { directory: /var/log/otelcol }

service:
  extensions:
  - file_storage
  pipelines:
    logs:
      receivers: [ filelog ]
      processors: [ resourcedetection, batch ]
      exporters: [ coralogix ]
    metrics:
      receivers: [ hostmetrics, otlp, spanmetrics ]
      processors: [ resourcedetection, batch ]
      exporters: [ coralogix ]
    traces:
      receivers: [ otlp ]
      processors: [ resourcedetection, batch ]
      exporters: [ coralogix, spanmetrics ]
    logs/resource_catalog:
      exporters:
        - coralogix/resource_catalog
      processors:
        - resource/metadata
        - resourcedetection/entity
        - transform/entity-event
      receivers:
        - hostmetrics
EOF


# Check status

sudo systemctl restart otelcol-contrib
sudo systemctl status otelcol-contrib


echo "================"
echo "OpenTelemetry Collector Contrib installation complete."
echo "================"
sudo journalctl -u otelcol-contrib -n 50 --no-pager


echo "================END======================="