
# OpenTelemetry Custom Metrics Application

This project demonstrates how to send custom metrics (<PERSON>, Gauge, and Histogram) to an OpenTelemetry Collector using .NET.

## Prerequisites

### Install .NET SDK

#### Windows
1. Download .NET 9.0 SDK from [https://dotnet.microsoft.com/download](https://dotnet.microsoft.com/download)
2. Run the installer and follow the setup wizard

#### macOS
```bash
# Using Homebrew
brew install --cask dotnet

# Or download from Microsoft
curl -sSL https://dot.net/v1/dotnet-install.sh | bash /dev/stdin --channel 9.0
```

#### Linux (Ubuntu/Debian)
```bash
# Add Microsoft package repository
wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
rm packages-microsoft-prod.deb

# Install .NET SDK
sudo apt-get update
sudo apt-get install -y dotnet-sdk-9.0
```

Verify installation:
```bash
dotnet --version
```

## Project Setup

### 1. Create New Project
```bash
# Create new console application
dotnet new console -n MyOpenTelemetryApp
cd MyOpenTelemetryApp
```

### 2. Add NuGet Dependencies
```bash
# Add OpenTelemetry packages
dotnet add package OpenTelemetry --version 1.12.0
dotnet add package OpenTelemetry.Exporter.OpenTelemetryProtocol --version 1.12.0
dotnet add package OpenTelemetry.Exporter.Console --version 1.12.0
dotnet add package System.Diagnostics.DiagnosticSource --version 9.0.0
```

### for this project the dependencies are already added in the csproj file, so you can skip the previous step and directly go to the next step below

### 3. Export ENV variables
```bash
export CX_ENDPOINT=https://ingress.ap1.coralogix.com/v1/metrics (update your endpoint)
export CX_TOKEN=your_actual_api_key
```
### 4. Restore Dependencies
```bash
dotnet restore
```

### 5. Build Project
```bash
dotnet build
```

### 6. Run Application
```bash
dotnet run
```

## Project Structure

```
MyOpenTelemetryApp/
├── Program.cs                 # Main application entry point
├── MyOpenTelemetryApp.csproj # Project file with dependencies
└── README.md                 # This file
```



### Metrics Exported
- **Counter**: `my.counter` 
- **Gauge**: `my.gauge`
- **Histogram**: `my.histogram`


### Verify in Coralogix
- Go to Hosted Grafana and search for the metrics my_counter, my_gauge, and my_histogram to see the exported metrics.

