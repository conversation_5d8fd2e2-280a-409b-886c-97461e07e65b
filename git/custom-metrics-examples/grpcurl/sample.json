{"resource_metrics": {"scope_metrics": {"metrics": [{"name": "grpc_sample_gauge1", "gauge": {"data_points": [{"as_double": 0.8, "attributes": [{"key": "service.name", "value": {"string_value": "test-service"}}], "start_time_unix_nano": 1657079957000000000, "time_unix_nano": 1657079957000000000}]}}, {"name": "grpc_sample_counter1", "gauge": {"data_points": [{"as_int": 100, "attributes": [{"key": "service.name", "value": {"string_value": "test-service"}}], "start_time_unix_nano": 1657079957000000000, "time_unix_nano": 1657079957000000000}]}}]}}}