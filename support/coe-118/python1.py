import time
import random
from opentelemetry import metrics
from opentelemetry.exporter.otlp.proto.http.metric_exporter import OTLPMetricExporter
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
from opentelemetry.sdk.resources import Resource

# Setup OTLP Exporter (point to your Coralogix OTLP endpoint)
exporter = OTLPMetricExporter(
    endpoint="https://ingress.ap1.coralogix.com/v1/metrics",
    headers={"Authorization": "Bearer Your_Coralogix_API_Key"},
)

reader = PeriodicExportingMetricReader(exporter, export_interval_millis=5000)
provider = MeterProvider(resource=Resource.create({"service.name": "broken-demo"}), metric_readers=[reader])
metrics.set_meter_provider(provider)

meter = metrics.get_meter("broken_demo")

# ❌ Wrong: Using UpDownCounter for backlog (a gauge)
backlog_counter = meter.create_up_down_counter(
    name="sample_backlog_per_instance",
    description="Backlog per instance (BROKEN)",
    unit="1",
)

print("Sending backlog metric (BROKEN version)...")
while True:
    value = random.randint(400, 600)  # pretend backlog
    backlog_counter.add(value, attributes={"WorkerName": "demo-worker"})
    print(f"Sent backlog value={value} (but this will ACCUMULATE)")
    time.sleep(5)
