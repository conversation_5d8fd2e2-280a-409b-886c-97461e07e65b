import time
import random
from opentelemetry import metrics
from opentelemetry.metrics import Observation  
from opentelemetry.exporter.otlp.proto.http.metric_exporter import OTLPMetricExporter
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
from opentelemetry.sdk.resources import Resource

PRIVATE_KEY = "YOUR_PRIVATE_KEY"

exporter = OTLPMetricExporter(
    endpoint="https://ingress.ap1.coralogix.com/v1/metrics",
    headers={"Authorization": "Bearer your_private_key"},
)

reader = PeriodicExportingMetricReader(exporter, export_interval_millis=5000)
provider = MeterProvider(resource=Resource.create({"service.name": "fixed-demo"}), metric_readers=[reader])
metrics.set_meter_provider(provider)

meter = metrics.get_meter("fixed_demo")

# ✅ Correct: ObservableGauge reports the current value
def backlog_callback(options):
    value = random.randint(400, 600)
    print(f"Reporting backlog value={value} (correct gauge)")
    return [Observation(value, {"WorkerName": "demo-worker"})]

meter.create_observable_gauge(
    name="sample_backlog_per_instance",
    description="Backlog per instance (FIXED)",
    unit="1",
    callbacks=[backlog_callback],
)

print("Sending backlog metric (FIXED version)...")
while True:
    time.sleep(5)  # callback fires automatically
