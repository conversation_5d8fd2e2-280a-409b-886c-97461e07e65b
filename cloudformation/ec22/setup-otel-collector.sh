#!/bin/bash
set -e

echo "Setting up OpenTelemetry Collector (latest version)..."

cd /opt/simple-nodejs

# Get the latest release version from GitHub API
echo "Fetching latest OpenTelemetry Collector version..."
LATEST_VERSION=$(curl -s https://api.github.com/repos/open-telemetry/opentelemetry-collector-releases/releases/latest | grep '"tag_name":' | sed -E 's/.*"([^"]+)".*/\1/')

if [ -z "$LATEST_VERSION" ]; then
    echo "Failed to fetch latest version, using fallback version v0.91.0"
    LATEST_VERSION="v0.91.0"
fi

echo "Installing OpenTelemetry Collector version: $LATEST_VERSION"

# Download and install OpenTelemetry Collector Contrib (latest version)
DOWNLOAD_URL="https://github.com/open-telemetry/opentelemetry-collector-releases/releases/download/${LATEST_VERSION}/otelcol-contrib_linux_amd64.tar.gz"
wget $DOWNLOAD_URL -O otelcol-contrib_linux_amd64.tar.gz

tar -xzf otelcol-contrib_linux_amd64.tar.gz
mv otelcol-contrib /usr/local/bin/
chmod +x /usr/local/bin/otelcol-contrib

# Verify installation
/usr/local/bin/otelcol-contrib --version

# Create OTEL Collector configuration
cat > otel-collector-config.yaml << 'EOF'
receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318
  
  # Host metrics for system monitoring
  hostmetrics:
    collection_interval: 30s
    scrapers:
      cpu:
        metrics:
          system.cpu.utilization:
            enabled: true
      memory:
        metrics:
          system.memory.utilization:
            enabled: true
      disk:
        metrics:
          system.disk.io.time:
            enabled: true
      network:
        metrics:
          system.network.io:
            enabled: true
      filesystem:
        metrics:
          system.filesystem.utilization:
            enabled: true
  
  # File log receiver for application logs
  filelog:
    include:
      - /var/log/messages
      - /var/log/secure
      - /opt/simple-nodejs/logs/*.log
    operators:
      - type: json_parser
        id: json_parser
        if: 'body matches "^\\{"'
        parse_from: body
        parse_to: attributes

  # Journald receiver for systemd service logs
  journald:
    directory: /var/log/journal
    units:
      - simple-nodejs
      - otel-collector
    operators:
      - type: json_parser
        id: journald_json_parser
        if: 'body matches "^\\{"'
        parse_from: body
        parse_to: attributes

processors:
  batch:
    timeout: 1s
    send_batch_size: 1024
  
  memory_limiter:
    limit_mib: 256
  
  # Resource processor to add additional attributes
  resource:
    attributes:
      - key: deployment.environment
        value: "demo"
        action: insert
      - key: service.instance.id
        from_attribute: host.name
        action: insert

  # Span metrics processor
  spanmetrics:
    metrics_exporter: coralogix
    latency_histogram_buckets: [100us, 1ms, 2ms, 6ms, 10ms, 100ms, 250ms, 500ms, 1s]
    dimensions_cache_size: 1000
    aggregation_temporality: "AGGREGATION_TEMPORALITY_CUMULATIVE"

exporters:
  # Coralogix exporter
  coralogix:
    domain: "coralogix.ap-southeast-1.coralogix.com"
    private_key: "${CORALOGIX_PRIVATE_KEY}"
    application_name: "${CORALOGIX_APPLICATION_NAME}"
    subsystem_name: "${CORALOGIX_SUBSYSTEM_NAME}"
    timeout: 30s
  
  # Logging exporter for debugging
  logging:
    loglevel: info
    sampling_initial: 5
    sampling_thereafter: 200

service:
  pipelines:
    traces:
      receivers: [otlp]
      processors: [memory_limiter, resource, batch, spanmetrics]
      exporters: [coralogix, logging]
    
    metrics:
      receivers: [otlp, hostmetrics]
      processors: [memory_limiter, resource, batch]
      exporters: [coralogix, logging]
    
    logs:
      receivers: [otlp, filelog, journald]
      processors: [memory_limiter, resource, batch]
      exporters: [coralogix, logging]

  extensions: []
  
  telemetry:
    logs:
      level: "info"
    metrics:
      address: 0.0.0.0:8888
EOF

# Create logs directory
mkdir -p /opt/simple-nodejs/logs

# Create systemd service for OTEL Collector
cat > /etc/systemd/system/otel-collector.service << 'EOF'
[Unit]
Description=OpenTelemetry Collector
After=network.target
Before=simple-nodejs.service

[Service]
Type=simple
User=root
ExecStart=/usr/local/bin/otelcol-contrib --config=/opt/simple-nodejs/otel-collector-config.yaml
Restart=always
RestartSec=5
EnvironmentFile=/opt/simple-nodejs/.env
StandardOutput=journal
StandardError=journal
SyslogIdentifier=otel-collector

# Resource limits
MemoryLimit=512M
CPUQuota=50%

[Install]
WantedBy=multi-user.target
EOF

# Create a simple health check script for OTEL collector
cat > /opt/simple-nodejs/check-otel-health.sh << 'EOF'
#!/bin/bash
# Simple health check for OTEL collector

OTEL_HEALTH_URL="http://localhost:8888/metrics"

if curl -s -f "$OTEL_HEALTH_URL" > /dev/null 2>&1; then
    echo "OTEL Collector is healthy"
    exit 0
else
    echo "OTEL Collector is not responding"
    exit 1
fi
EOF

chmod +x /opt/simple-nodejs/check-otel-health.sh

echo "OpenTelemetry Collector setup completed"
echo "Installed version: $LATEST_VERSION"
