#!/bin/bash

# Simple Node.js Service CloudFormation Stack Deployment Script
# This script deploys the EC2 instance with Node.js service and OpenTelemetry monitoring

set -e

# Configuration
STACK_NAME="simple-nodejs-stack"
TEMPLATE_FILE="ec2.yaml"
REGION="ap-south-1"  # Change this to your preferred region

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if AWS CLI is installed and configured
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install it first."
        echo "Installation instructions: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html"
        exit 1
    fi
    
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI is not configured. Please run 'aws configure' first."
        exit 1
    fi
    
    print_success "AWS CLI is properly configured"
    
    # Display current AWS identity
    ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
    USER_ARN=$(aws sts get-caller-identity --query Arn --output text)
    print_status "Deploying as: $USER_ARN"
    print_status "Account ID: $ACCOUNT_ID"
}

# Function to validate CloudFormation template
validate_template() {
    print_status "Validating CloudFormation template..."
    
    if aws cloudformation validate-template --template-body file://$TEMPLATE_FILE --region $REGION > /dev/null; then
        print_success "Template validation passed"
    else
        print_error "Template validation failed"
        exit 1
    fi
}

# Function to check if key pair exists
check_key_pair() {
    local key_name=$1
    if aws ec2 describe-key-pairs --key-names "$key_name" --region $REGION > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to get user inputs
get_user_inputs() {
    echo
    print_status "Please provide the following parameters:"
    
    # Get Key Pair Name
    while true; do
        echo -n "Enter EC2 Key Pair Name: "
        read KEY_PAIR_NAME
        if [[ -z "$KEY_PAIR_NAME" ]]; then
            print_error "Key Pair Name is required"
            continue
        fi
        
        if check_key_pair "$KEY_PAIR_NAME"; then
            print_success "Key pair '$KEY_PAIR_NAME' found"
            break
        else
            print_error "Key pair '$KEY_PAIR_NAME' not found in region $REGION"
            echo "Available key pairs:"
            aws ec2 describe-key-pairs --region $REGION --query 'KeyPairs[].KeyName' --output table 2>/dev/null || echo "No key pairs found"
            echo -n "Do you want to continue anyway? (y/N): "
            read continue_anyway
            if [[ "$continue_anyway" =~ ^[Yy]$ ]]; then
                break
            fi
        fi
    done
    
    # Get Instance Type (with default)
    echo -n "Enter Instance Type (default: t3.medium): "
    read INSTANCE_TYPE
    if [[ -z "$INSTANCE_TYPE" ]]; then
        INSTANCE_TYPE="t3.medium"
    fi
    
    # Get Coralogix Private Key
    echo -n "Enter Coralogix Private Key: "
    read -s CORALOGIX_PRIVATE_KEY
    echo
    if [[ -z "$CORALOGIX_PRIVATE_KEY" ]]; then
        print_error "Coralogix Private Key is required"
        exit 1
    fi
    
    # Get Coralogix Application Name (with default)
    echo -n "Enter Coralogix Application Name (default: simple-nodejs-demo): "
    read CORALOGIX_APP_NAME
    if [[ -z "$CORALOGIX_APP_NAME" ]]; then
        CORALOGIX_APP_NAME="simple-nodejs-demo"
    fi
    
    # Get Coralogix Subsystem Name (with default)
    echo -n "Enter Coralogix Subsystem Name (default: ec2-nodejs): "
    read CORALOGIX_SUBSYSTEM_NAME
    if [[ -z "$CORALOGIX_SUBSYSTEM_NAME" ]]; then
        CORALOGIX_SUBSYSTEM_NAME="ec2-nodejs"
    fi
    
    # Confirm deployment
    echo
    print_status "Deployment Summary:"
    echo "  Stack Name: $STACK_NAME"
    echo "  Region: $REGION"
    echo "  Instance Type: $INSTANCE_TYPE"
    echo "  Key Pair: $KEY_PAIR_NAME"
    echo "  Coralogix App: $CORALOGIX_APP_NAME"
    echo "  Coralogix Subsystem: $CORALOGIX_SUBSYSTEM_NAME"
    echo
    echo -n "Do you want to proceed with deployment? (y/N): "
    read confirm_deploy
    if [[ ! "$confirm_deploy" =~ ^[Yy]$ ]]; then
        print_status "Deployment cancelled"
        exit 0
    fi
}

# Function to deploy the stack
deploy_stack() {
    print_status "Deploying CloudFormation stack: $STACK_NAME"
    
    aws cloudformation deploy \
        --template-file $TEMPLATE_FILE \
        --stack-name $STACK_NAME \
        --parameter-overrides \
            KeyPairName=$KEY_PAIR_NAME \
            InstanceType=$INSTANCE_TYPE \
            CoralogixPrivateKey=$CORALOGIX_PRIVATE_KEY \
            CoralogixApplicationName=$CORALOGIX_APP_NAME \
            CoralogixSubsystemName=$CORALOGIX_SUBSYSTEM_NAME \
        --capabilities CAPABILITY_NAMED_IAM \
        --region $REGION
    
    if [ $? -eq 0 ]; then
        print_success "Stack deployment completed successfully!"
    else
        print_error "Stack deployment failed!"
        exit 1
    fi
}

# Function to get stack outputs
get_stack_outputs() {
    print_status "Retrieving stack outputs..."
    
    OUTPUTS=$(aws cloudformation describe-stacks \
        --stack-name $STACK_NAME \
        --region $REGION \
        --query 'Stacks[0].Outputs' \
        --output table)
    
    echo
    print_success "Stack Outputs:"
    echo "$OUTPUTS"
    
    # Get specific outputs for easy access
    INSTANCE_ID=$(aws cloudformation describe-stacks \
        --stack-name $STACK_NAME \
        --region $REGION \
        --query 'Stacks[0].Outputs[?OutputKey==`InstanceId`].OutputValue' \
        --output text)
    
    PUBLIC_IP=$(aws cloudformation describe-stacks \
        --stack-name $STACK_NAME \
        --region $REGION \
        --query 'Stacks[0].Outputs[?OutputKey==`InstancePublicIP`].OutputValue' \
        --output text)
    
    VPC_ID=$(aws cloudformation describe-stacks \
        --stack-name $STACK_NAME \
        --region $REGION \
        --query 'Stacks[0].Outputs[?OutputKey==`VPCId`].OutputValue' \
        --output text)
    
    SUBNET_ID=$(aws cloudformation describe-stacks \
        --stack-name $STACK_NAME \
        --region $REGION \
        --query 'Stacks[0].Outputs[?OutputKey==`SubnetId`].OutputValue' \
        --output text)
    
    echo
    print_success "Quick Access Information:"
    echo "Instance ID: $INSTANCE_ID"
    echo "Public IP: $PUBLIC_IP"
    echo "VPC ID: $VPC_ID"
    echo "Subnet ID: $SUBNET_ID"
    echo
    echo "Service URLs:"
    echo "  Node.js Service: http://$PUBLIC_IP:3000"
    echo "  Health Check:    http://$PUBLIC_IP:3000/health"
    echo "  API Endpoint:    http://$PUBLIC_IP:3000/api/data"
    echo
    echo "SSH Access:"
    echo "  ssh -i ~/.ssh/$KEY_PAIR_NAME.pem ec2-user@$PUBLIC_IP"
    echo
    echo "SSM Session Manager:"
    echo "  aws ssm start-session --target $INSTANCE_ID --region $REGION"
}

# Function to wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready (this may take 5-10 minutes)..."
    
    PUBLIC_IP=$(aws cloudformation describe-stacks \
        --stack-name $STACK_NAME \
        --region $REGION \
        --query 'Stacks[0].Outputs[?OutputKey==`InstancePublicIP`].OutputValue' \
        --output text)
    
    max_attempts=60
    attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -s -f "http://$PUBLIC_IP:3000/health" > /dev/null 2>&1; then
            print_success "Node.js service is ready!"
            
            # Test the service
            echo
            print_status "Testing service endpoints..."
            echo "Health check response:"
            curl -s "http://$PUBLIC_IP:3000/health" | jq . 2>/dev/null || curl -s "http://$PUBLIC_IP:3000/health"
            
            return 0
        fi
        
        attempt=$((attempt + 1))
        echo -n "."
        sleep 10
    done
    
    print_warning "Service may still be starting up. Check manually using the URLs above."
}

# Main execution
main() {
    echo "=========================================="
    echo "Simple Node.js Service Stack Deployment"
    echo "=========================================="
    echo
    
    check_aws_cli
    validate_template
    get_user_inputs
    deploy_stack
    get_stack_outputs
    wait_for_services
    
    echo
    print_success "Deployment completed! Your Node.js service is now running."
    echo
    print_status "Next steps:"
    echo "1. Test the service using the URLs provided above"
    echo "2. Check Coralogix for telemetry data"
    echo "3. Use SSH or SSM Session Manager to connect to the instance"
    echo "4. Run the health check: /opt/simple-nodejs/health-check.sh"
    echo
    print_status "To delete the stack later, run:"
    echo "  aws cloudformation delete-stack --stack-name $STACK_NAME --region $REGION"
}

# Run main function
main "$@"
