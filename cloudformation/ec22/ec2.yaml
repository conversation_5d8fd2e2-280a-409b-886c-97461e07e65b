AWSTemplateFormatVersion: '2010-09-09'
Description: 'Simple EC2 instance with Node.js service, VPC, and OpenTelemetry monitoring'

Parameters:
  InstanceType:
    Type: String
    Default: t3.medium
    AllowedValues:
      - t3.small
      - t3.medium
      - t3.large
      - t3.xlarge
      - m5.large
      - m5.xlarge
    Description: EC2 instance type
    
  KeyPairName:
    Type: AWS::EC2::KeyPair::KeyName
    Description: EC2 Key Pair for SSH access
    
  CoralogixPrivateKey:
    Type: String
    NoEcho: true
    Description: Coralogix Private Key for telemetry data
    
  CoralogixApplicationName:
    Type: String
    Default: simple-nodejs-demo
    Description: Application name for Coralogix
    
  CoralogixSubsystemName:
    Type: String
    Default: ec2-nodejs
    Description: Subsystem name for Coralogix

Resources:
  # VPC
  VPC:
    Type: AWS::EC2::VPC
    Properties:
      CidrBlock: 10.0.0.0/16
      EnableDnsHostnames: true
      EnableDnsSupport: true
      Tags:
        - Key: Name
          Value: SimpleNodeJS-VPC

  # Internet Gateway
  InternetGateway:
    Type: AWS::EC2::InternetGateway
    Properties:
      Tags:
        - Key: Name
          Value: SimpleNodeJS-IGW

  # Attach Internet Gateway to VPC
  InternetGatewayAttachment:
    Type: AWS::EC2::VPCGatewayAttachment
    Properties:
      InternetGatewayId: !Ref InternetGateway
      VpcId: !Ref VPC

  # Public Subnet
  PublicSubnet:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref VPC
      AvailabilityZone: !Select [0, !GetAZs '']
      CidrBlock: ********/24
      MapPublicIpOnLaunch: true
      Tags:
        - Key: Name
          Value: SimpleNodeJS-PublicSubnet

  # Route Table
  PublicRouteTable:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId: !Ref VPC
      Tags:
        - Key: Name
          Value: SimpleNodeJS-PublicRoutes

  # Default Public Route
  DefaultPublicRoute:
    Type: AWS::EC2::Route
    DependsOn: InternetGatewayAttachment
    Properties:
      RouteTableId: !Ref PublicRouteTable
      DestinationCidrBlock: 0.0.0.0/0
      GatewayId: !Ref InternetGateway

  # Associate Subnet with Route Table
  PublicSubnetRouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      RouteTableId: !Ref PublicRouteTable
      SubnetId: !Ref PublicSubnet

  # IAM Role for EC2 instance
  EC2Role:
    Type: AWS::IAM::Role
    Properties:
      RoleName: SimpleNodeJS-EC2Role
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ec2.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore
        - arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy
      Policies:
        - PolicyName: CloudWatchLogs
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:DescribeLogStreams
                  - logs:DescribeLogGroups
                Resource: '*'

  # Instance Profile
  EC2InstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Properties:
      Roles:
        - !Ref EC2Role

  # Security Group
  SecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: SimpleNodeJS-SecurityGroup
      GroupDescription: Security group for simple Node.js service
      VpcId: !Ref VPC
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 22
          ToPort: 22
          CidrIp: 0.0.0.0/0
          Description: SSH access
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 0.0.0.0/0
          Description: HTTP access
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0
          Description: HTTPS access
        - IpProtocol: tcp
          FromPort: 3000
          ToPort: 3000
          CidrIp: 0.0.0.0/0
          Description: Node.js service
        - IpProtocol: tcp
          FromPort: 4317
          ToPort: 4318
          CidrIp: 0.0.0.0/0
          Description: OTEL Collector OTLP
      SecurityGroupEgress:
        - IpProtocol: -1
          CidrIp: 0.0.0.0/0
          Description: All outbound traffic

  # EC2 Instance
  EC2Instance:
    Type: AWS::EC2::Instance
    Properties:
      ImageId: ami-0c02fb55956c7d316  # Amazon Linux 2023 AMI (update as needed)
      InstanceType: !Ref InstanceType
      KeyName: !Ref KeyPairName
      SubnetId: !Ref PublicSubnet
      SecurityGroupIds:
        - !Ref SecurityGroup
      IamInstanceProfile: !Ref EC2InstanceProfile
      Tags:
        - Key: Name
          Value: SimpleNodeJS-Instance
        - Key: Environment
          Value: demo
      UserData:
        Fn::Base64: 
          Fn::Sub: |
            #!/bin/bash
            set -e
            
            # Update system and install packages
            yum update -y
            yum install -y docker git wget curl unzip nodejs npm
            
            # Start Docker
            systemctl start docker
            systemctl enable docker
            usermod -a -G docker ec2-user
            
            # Create application directory
            mkdir -p /opt/simple-nodejs
            cd /opt/simple-nodejs
            
            # Create environment file
            cat > .env << 'ENVEOF'
            OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4317
            OTEL_EXPORTER_OTLP_PROTOCOL=grpc
            OTEL_SERVICE_NAME=simple-nodejs
            OTEL_RESOURCE_ATTRIBUTES=service.name=simple-nodejs,service.version=1.0.0
            CORALOGIX_PRIVATE_KEY=${CoralogixPrivateKey}
            CORALOGIX_APPLICATION_NAME=${CoralogixApplicationName}
            CORALOGIX_SUBSYSTEM_NAME=${CoralogixSubsystemName}
            ENVEOF
            
            # Create and run setup scripts inline
            # Create main setup script
            cat > main-setup.sh << 'MAIN_SETUP_EOF'
            #!/bin/bash
            set -e

            echo "Starting simple Node.js service setup..."
            cd /opt/simple-nodejs

            # Setup OTEL Collector
            echo "Setting up OpenTelemetry Collector..."
            LATEST_VERSION=$(curl -s https://api.github.com/repos/open-telemetry/opentelemetry-collector-releases/releases/latest | grep '"tag_name":' | sed -E 's/.*"([^"]+)".*/\1/' || echo "v0.91.0")
            wget "https://github.com/open-telemetry/opentelemetry-collector-releases/releases/download/$LATEST_VERSION/otelcol-contrib_linux_amd64.tar.gz"
            tar -xzf otelcol-contrib_linux_amd64.tar.gz
            mv otelcol-contrib /usr/local/bin/
            chmod +x /usr/local/bin/otelcol-contrib

            # Create OTEL config
            cat > otel-collector-config.yaml << 'OTEL_CONFIG_EOF'
            receivers:
              otlp:
                protocols:
                  grpc:
                    endpoint: 0.0.0.0:4317
                  http:
                    endpoint: 0.0.0.0:4318
              hostmetrics:
                collection_interval: 30s
                scrapers:
                  cpu:
                  memory:
                  disk:
                  network:
                  filesystem:
            processors:
              batch:
                timeout: 1s
                send_batch_size: 1024
              memory_limiter:
                limit_mib: 256
            exporters:
              coralogix:
                domain: "coralogix.ap-southeast-1.coralogix.com"
                private_key: "$CORALOGIX_PRIVATE_KEY"
                application_name: "$CORALOGIX_APPLICATION_NAME"
                subsystem_name: "$CORALOGIX_SUBSYSTEM_NAME"
                timeout: 30s
              logging:
                loglevel: info
            service:
              pipelines:
                traces:
                  receivers: [otlp]
                  processors: [memory_limiter, batch]
                  exporters: [coralogix, logging]
                metrics:
                  receivers: [otlp, hostmetrics]
                  processors: [memory_limiter, batch]
                  exporters: [coralogix, logging]
                logs:
                  receivers: [otlp]
                  processors: [memory_limiter, batch]
                  exporters: [coralogix, logging]
            OTEL_CONFIG_EOF

            # Create OTEL systemd service
            cat > /etc/systemd/system/otel-collector.service << 'OTEL_SERVICE_EOF'
            [Unit]
            Description=OpenTelemetry Collector
            After=network.target

            [Service]
            Type=simple
            User=root
            ExecStart=/usr/local/bin/otelcol-contrib --config=/opt/simple-nodejs/otel-collector-config.yaml
            Restart=always
            RestartSec=5
            EnvironmentFile=/opt/simple-nodejs/.env

            [Install]
            WantedBy=multi-user.target
            OTEL_SERVICE_EOF

            # Setup Node.js service
            echo "Setting up Node.js service..."
            mkdir -p nodejs-service
            cd nodejs-service

            # Create package.json
            cat > package.json << 'PACKAGE_EOF'
            {
              "name": "simple-nodejs-service",
              "version": "1.0.0",
              "main": "app.js",
              "dependencies": {
                "express": "^4.18.2",
                "@opentelemetry/api": "^1.8.0",
                "@opentelemetry/sdk-node": "^0.51.0",
                "@opentelemetry/auto-instrumentations-node": "^0.46.0",
                "@opentelemetry/exporter-otlp-grpc": "^0.51.0"
              }
            }
            PACKAGE_EOF

            npm install

            # Create Node.js app (truncated for space - see setup script for full version)
            cat > app.js << 'APP_EOF'
            const { NodeSDK } = require('@opentelemetry/sdk-node');
            const { getNodeAutoInstrumentations } = require('@opentelemetry/auto-instrumentations-node');
            const { OTLPTraceExporter } = require('@opentelemetry/exporter-otlp-grpc');

            const sdk = new NodeSDK({
              traceExporter: new OTLPTraceExporter({
                url: process.env.OTEL_EXPORTER_OTLP_ENDPOINT || 'http://localhost:4317',
              }),
              instrumentations: [getNodeAutoInstrumentations()],
            });

            sdk.start();

            const express = require('express');
            const app = express();
            const port = 3000;

            app.use(express.json());

            app.get('/health', (req, res) => {
              res.json({
                service: 'simple-nodejs',
                status: 'healthy',
                timestamp: new Date().toISOString()
              });
            });

            app.get('/', (req, res) => {
              res.json({
                service: 'simple-nodejs',
                message: 'Welcome to Simple Node.js Service!',
                endpoints: ['GET /', 'GET /health', 'GET /api/data', 'POST /api/data']
              });
            });

            app.get('/api/data', (req, res) => {
              res.json({
                service: 'simple-nodejs',
                data: 'Hello from Simple Node.js API!',
                timestamp: new Date().toISOString()
              });
            });

            app.listen(port, '0.0.0.0', () => {
              console.log('Simple Node.js service listening on port ' + port);
            });
            APP_EOF

            # Create Node.js systemd service
            cat > /etc/systemd/system/simple-nodejs.service << 'NODEJS_SERVICE_EOF'
            [Unit]
            Description=Simple Node.js Service
            After=network.target otel-collector.service

            [Service]
            Type=simple
            User=ec2-user
            WorkingDirectory=/opt/simple-nodejs/nodejs-service
            ExecStart=/usr/bin/node app.js
            Restart=always
            RestartSec=5
            EnvironmentFile=/opt/simple-nodejs/.env

            [Install]
            WantedBy=multi-user.target
            NODEJS_SERVICE_EOF

            # Start services
            systemctl daemon-reload
            systemctl enable otel-collector
            systemctl start otel-collector
            sleep 5
            systemctl enable simple-nodejs
            systemctl start simple-nodejs

            echo "Setup completed successfully!"
            MAIN_SETUP_EOF

            chmod +x main-setup.sh
            ./main-setup.sh
            
            echo "Setup completed" > /var/log/setup-complete.log

Outputs:
  InstanceId:
    Description: EC2 Instance ID
    Value: !Ref EC2Instance
    Export:
      Name: SimpleNodeJS-InstanceId
      
  InstancePublicIP:
    Description: Public IP address of the EC2 instance
    Value: !GetAtt EC2Instance.PublicIp
    Export:
      Name: SimpleNodeJS-PublicIP
      
  InstancePrivateIP:
    Description: Private IP address of the EC2 instance
    Value: !GetAtt EC2Instance.PrivateIp
    Export:
      Name: SimpleNodeJS-PrivateIP

  VPCId:
    Description: VPC ID
    Value: !Ref VPC
    Export:
      Name: SimpleNodeJS-VPCId

  SubnetId:
    Description: Public Subnet ID
    Value: !Ref PublicSubnet
    Export:
      Name: SimpleNodeJS-SubnetId

  NodeJSServiceURL:
    Description: Node.js service URL
    Value: !Sub 'http://${EC2Instance.PublicIp}:3000'
    Export:
      Name: SimpleNodeJS-ServiceURL

  HTTPServiceURL:
    Description: HTTP service URL (port 80)
    Value: !Sub 'http://${EC2Instance.PublicIp}'
    Export:
      Name: SimpleNodeJS-HTTPURL

  SSMSessionCommand:
    Description: Command to connect via SSM Session Manager
    Value: !Sub 'aws ssm start-session --target ${EC2Instance}'
    Export:
      Name: SimpleNodeJS-SSMCommand
