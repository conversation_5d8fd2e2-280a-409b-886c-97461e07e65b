# Simple Node.js Service with OpenTelemetry on EC2

This directory contains a simplified version of the EC2 deployment with only a Node.js service, complete VPC setup, and OpenTelemetry monitoring.

## Overview

This setup creates:
- **VPC with public subnet** - Complete networking infrastructure
- **EC2 instance** - Running Amazon Linux 2023
- **Node.js service** - Simple REST API with health checks
- **OpenTelemetry Collector** - Latest version with Coralogix integration
- **Security Group** - Allows HTTP/HTTPS and SSH access
- **Automated deployment** - One-command deployment and cleanup

## Architecture

```
Internet Gateway
       |
   Public Subnet (********/24)
       |
   EC2 Instance
   ├── Node.js Service (Port 3000)
   ├── OpenTelemetry Collector (Ports 4317/4318)
   └── HTTP/HTTPS Access (Ports 80/443)
```

## Files

- `ec2.yaml` - CloudFormation template with VPC and EC2 setup
- `deploy.sh` - Automated deployment script
- `delete.sh` - Cleanup script
- `main-setup.sh` - Main orchestration script (runs on EC2)
- `setup-nodejs-service.sh` - Node.js service setup
- `setup-otel-collector.sh` - OpenTelemetry Collector setup (latest version)

## Prerequisites

1. **AWS CLI** installed and configured
2. **EC2 Key Pair** created in your target region
3. **Coralogix account** with private key
4. **jq** (optional, for pretty JSON output)

## Quick Start

1. **Clone and navigate to the directory:**
   ```bash
   cd ec22
   ```

2. **Make scripts executable:**
   ```bash
   chmod +x *.sh
   ```

3. **Deploy the stack:**
   ```bash
   ./deploy.sh
   ```

4. **Follow the prompts to enter:**
   - EC2 Key Pair name
   - Instance type (default: t3.medium)
   - Coralogix private key
   - Coralogix application name
   - Coralogix subsystem name

5. **Wait for deployment** (5-10 minutes)

6. **Access your service:**
   - Node.js Service: `http://YOUR_PUBLIC_IP:3000`
   - Health Check: `http://YOUR_PUBLIC_IP:3000/health`
   - API Endpoint: `http://YOUR_PUBLIC_IP:3000/api/data`

## Service Endpoints

The Node.js service provides the following endpoints:

- `GET /` - Welcome message with endpoint list
- `GET /health` - Health check endpoint
- `GET /api/data` - Get sample data
- `POST /api/data` - Submit data (requires JSON body)
- `GET /api/error` - Simulate random errors for testing

## Monitoring

### OpenTelemetry Collector

- **Version**: Latest (automatically fetched)
- **Ports**: 4317 (gRPC), 4318 (HTTP)
- **Exporters**: Coralogix, Console logging
- **Receivers**: OTLP, Host metrics, File logs, Journald

### Telemetry Data

The setup automatically collects:
- **Traces** - HTTP requests, database calls, external API calls
- **Metrics** - System metrics, application metrics, custom metrics
- **Logs** - Application logs, system logs, structured JSON logs

## Management Commands

### On the EC2 Instance

```bash
# Health check
/opt/simple-nodejs/health-check.sh

# View service logs
journalctl -u simple-nodejs -f

# View OTEL collector logs
journalctl -u otel-collector -f

# Check service status
systemctl status simple-nodejs
systemctl status otel-collector

# Restart services
sudo systemctl restart simple-nodejs
sudo systemctl restart otel-collector
```

### From Local Machine

```bash
# SSH into instance
ssh -i ~/.ssh/YOUR_KEY.pem ec2-user@YOUR_PUBLIC_IP

# SSM Session Manager (no SSH key needed)
aws ssm start-session --target YOUR_INSTANCE_ID --region YOUR_REGION

# Delete the entire stack
./delete.sh
```

## Testing the Service

### Basic Health Check
```bash
curl http://YOUR_PUBLIC_IP:3000/health
```

### API Testing
```bash
# Get data
curl http://YOUR_PUBLIC_IP:3000/api/data

# Post data
curl -X POST http://YOUR_PUBLIC_IP:3000/api/data \
  -H "Content-Type: application/json" \
  -d '{"test": "data", "timestamp": "2024-01-01"}'

# Simulate errors
curl http://YOUR_PUBLIC_IP:3000/api/error
```

### Load Testing
```bash
# Simple load test
for i in {1..10}; do
  curl -s http://YOUR_PUBLIC_IP:3000/api/data &
done
wait
```

## Configuration

### Environment Variables

The service uses these environment variables (set in `/opt/simple-nodejs/.env`):

```bash
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4317
OTEL_EXPORTER_OTLP_PROTOCOL=grpc
OTEL_SERVICE_NAME=simple-nodejs
OTEL_RESOURCE_ATTRIBUTES=service.name=simple-nodejs,service.version=1.0.0
CORALOGIX_PRIVATE_KEY=your-private-key
CORALOGIX_APPLICATION_NAME=simple-nodejs-demo
CORALOGIX_SUBSYSTEM_NAME=ec2-nodejs
```

### Security Groups

The security group allows:
- **SSH (22)** - From anywhere (0.0.0.0/0)
- **HTTP (80)** - From anywhere (0.0.0.0/0)
- **HTTPS (443)** - From anywhere (0.0.0.0/0)
- **Node.js (3000)** - From anywhere (0.0.0.0/0)
- **OTEL (4317-4318)** - From anywhere (0.0.0.0/0)

## Troubleshooting

### Service Not Starting

1. Check service status:
   ```bash
   systemctl status simple-nodejs
   systemctl status otel-collector
   ```

2. Check logs:
   ```bash
   journalctl -u simple-nodejs -n 50
   journalctl -u otel-collector -n 50
   ```

3. Verify dependencies:
   ```bash
   node --version
   npm --version
   /usr/local/bin/otelcol-contrib --version
   ```

### Network Issues

1. Check security group rules in AWS Console
2. Verify instance has public IP
3. Check VPC route table and internet gateway

### OTEL Collector Issues

1. Verify configuration:
   ```bash
   cat /opt/simple-nodejs/otel-collector-config.yaml
   ```

2. Test collector health:
   ```bash
   /opt/simple-nodejs/check-otel-health.sh
   ```

3. Check Coralogix connectivity:
   ```bash
   curl -v https://coralogix.ap-southeast-1.coralogix.com
   ```

## Cleanup

To delete all resources:

```bash
./delete.sh
```

This will:
- Delete the CloudFormation stack
- Terminate the EC2 instance
- Remove the VPC and all networking components
- Clean up IAM roles and security groups

## Cost Optimization

- **Instance Type**: Use t3.small for development, t3.medium for testing
- **Monitoring**: The setup includes resource limits for OTEL collector
- **Auto-shutdown**: Consider adding scheduled shutdown for development environments

## Security Notes

- The security group allows access from anywhere (0.0.0.0/0)
- For production, restrict access to specific IP ranges
- Use IAM roles instead of access keys where possible
- Enable VPC Flow Logs for network monitoring
- Consider using AWS Systems Manager Session Manager instead of SSH

## Next Steps

1. **Customize the Node.js application** for your specific use case
2. **Add SSL/TLS certificates** for HTTPS access
3. **Implement authentication** and authorization
4. **Add database connectivity** (RDS, DynamoDB)
5. **Set up CI/CD pipeline** for automated deployments
6. **Configure alerts** in Coralogix for monitoring



cxtp_Qd7ke2dhCmpybJuGZxBn5EYeuXUjlT