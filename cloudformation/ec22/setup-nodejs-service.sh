#!/bin/bash
set -e

echo "Setting up simple Node.js service..."

# Create Node.js service directory
mkdir -p /opt/simple-nodejs/nodejs-service
cd /opt/simple-nodejs/nodejs-service

# Create package.json with latest OpenTelemetry packages
cat > package.json << 'EOF'
{
  "name": "simple-nodejs-service",
  "version": "1.0.0",
  "description": "Simple Node.js service with OpenTelemetry",
  "main": "app.js",
  "scripts": {
    "start": "node app.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "@opentelemetry/api": "^1.8.0",
    "@opentelemetry/sdk-node": "^0.51.0",
    "@opentelemetry/auto-instrumentations-node": "^0.46.0",
    "@opentelemetry/exporter-otlp-grpc": "^0.51.0"
  }
}
EOF

# Create simple Node.js application
cat > app.js << 'EOF'
const { NodeSDK } = require('@opentelemetry/sdk-node');
const { getNodeAutoInstrumentations } = require('@opentelemetry/auto-instrumentations-node');
const { OTLPTraceExporter } = require('@opentelemetry/exporter-otlp-grpc');

const sdk = new NodeSDK({
  traceExporter: new OTLPTraceExporter({
    url: process.env.OTEL_EXPORTER_OTLP_ENDPOINT || 'http://localhost:4317',
  }),
  instrumentations: [getNodeAutoInstrumentations()],
});

sdk.start();

const express = require('express');
const app = express();
const port = 3000;

let requestCounter = 0;

app.use(express.json());

// Structured logging function
function logRequest(method, path, statusCode, requestId, duration = null, error = null) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    service: 'simple-nodejs',
    method: method,
    path: path,
    status_code: statusCode,
    request_id: requestId,
    duration_ms: duration,
    error: error,
    level: statusCode >= 500 ? 'ERROR' : statusCode >= 400 ? 'WARN' : 'INFO'
  };
  console.log(JSON.stringify(logEntry));
}

// Health check endpoint
app.get('/health', (req, res) => {
  const startTime = Date.now();
  const response = {
    service: 'simple-nodejs',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    requests_processed: requestCounter,
    uptime: process.uptime()
  };
  const duration = Date.now() - startTime;
  logRequest('GET', '/health', 200, 'health-check', duration);
  res.json(response);
});

// Root endpoint for HTTP access
app.get('/', (req, res) => {
  const startTime = Date.now();
  requestCounter++;
  const requestId = `nodejs-${requestCounter}`;
  
  const response = {
    service: 'simple-nodejs',
    message: 'Welcome to Simple Node.js Service!',
    timestamp: new Date().toISOString(),
    request_id: requestId,
    endpoints: [
      'GET / - This welcome message',
      'GET /health - Health check',
      'GET /api/data - Get sample data',
      'POST /api/data - Submit data'
    ]
  };
  
  const duration = Date.now() - startTime;
  logRequest('GET', '/', 200, requestId, duration);
  res.json(response);
});

// API endpoint for getting data
app.get('/api/data', (req, res) => {
  const startTime = Date.now();
  requestCounter++;
  const requestId = `nodejs-${requestCounter}`;

  // Simulate some processing time
  setTimeout(() => {
    const response = {
      service: 'simple-nodejs',
      data: {
        message: 'Hello from Simple Node.js API!',
        random_number: Math.floor(Math.random() * 1000),
        timestamp: new Date().toISOString(),
        request_id: requestId
      }
    };
    
    const duration = Date.now() - startTime;
    logRequest('GET', '/api/data', 200, requestId, duration);
    res.json(response);
  }, Math.random() * 100 + 50); // 50-150ms delay
});

// API endpoint for posting data
app.post('/api/data', (req, res) => {
  const startTime = Date.now();
  requestCounter++;
  const requestId = `nodejs-${requestCounter}`;

  // Simulate data validation
  if (!req.body || Object.keys(req.body).length === 0) {
    const duration = Date.now() - startTime;
    const errorMsg = 'Request body is required';
    logRequest('POST', '/api/data', 400, requestId, duration, errorMsg);
    return res.status(400).json({
      service: 'simple-nodejs',
      error: errorMsg,
      timestamp: new Date().toISOString(),
      request_id: requestId
    });
  }

  const response = {
    service: 'simple-nodejs',
    message: 'Data received successfully',
    received_data: req.body,
    timestamp: new Date().toISOString(),
    request_id: requestId
  };
  
  const duration = Date.now() - startTime;
  logRequest('POST', '/api/data', 201, requestId, duration);
  res.status(201).json(response);
});

// Error simulation endpoint
app.get('/api/error', (req, res) => {
  const startTime = Date.now();
  requestCounter++;
  const requestId = `nodejs-${requestCounter}`;

  // Simulate random errors
  const errorTypes = [
    { status: 400, error: 'Bad request: Invalid parameters' },
    { status: 401, error: 'Unauthorized: Authentication required' },
    { status: 403, error: 'Forbidden: Access denied' },
    { status: 404, error: 'Not found: Resource does not exist' },
    { status: 500, error: 'Internal server error: Something went wrong' },
    { status: 503, error: 'Service unavailable: Temporary maintenance' }
  ];

  const randomError = errorTypes[Math.floor(Math.random() * errorTypes.length)];
  const duration = Date.now() - startTime;

  logRequest('GET', '/api/error', randomError.status, requestId, duration, randomError.error);

  res.status(randomError.status).json({
    service: 'simple-nodejs',
    error: randomError.error,
    timestamp: new Date().toISOString(),
    request_id: requestId
  });
});

// Catch-all for undefined routes
app.use('*', (req, res) => {
  const startTime = Date.now();
  requestCounter++;
  const requestId = `nodejs-${requestCounter}`;
  
  const duration = Date.now() - startTime;
  const errorMsg = `Route not found: ${req.method} ${req.originalUrl}`;
  logRequest(req.method, req.originalUrl, 404, requestId, duration, errorMsg);
  
  res.status(404).json({
    service: 'simple-nodejs',
    error: errorMsg,
    timestamp: new Date().toISOString(),
    request_id: requestId,
    available_endpoints: [
      'GET /',
      'GET /health',
      'GET /api/data',
      'POST /api/data',
      'GET /api/error'
    ]
  });
});

app.listen(port, '0.0.0.0', () => {
  console.log(`Simple Node.js service listening on port ${port}`);
  console.log('Available endpoints:');
  console.log('  GET / - Welcome message');
  console.log('  GET /health - Health check');
  console.log('  GET /api/data - Get sample data');
  console.log('  POST /api/data - Submit data');
  console.log('  GET /api/error - Simulate errors');
});
EOF

# Install dependencies
npm install

# Create systemd service
cat > /etc/systemd/system/simple-nodejs.service << 'EOF'
[Unit]
Description=Simple Node.js Service
After=network.target otel-collector.service
Requires=otel-collector.service

[Service]
Type=simple
User=ec2-user
WorkingDirectory=/opt/simple-nodejs/nodejs-service
ExecStart=/usr/bin/node app.js
Restart=always
RestartSec=5
Environment=NODE_ENV=production
EnvironmentFile=/opt/simple-nodejs/.env
StandardOutput=journal
StandardError=journal
SyslogIdentifier=simple-nodejs

[Install]
WantedBy=multi-user.target
EOF

echo "Simple Node.js service setup completed"
