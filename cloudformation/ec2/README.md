# Distributed Services with OpenTelemetry on AWS EC2

This CloudFormation template deploys a complete distributed services architecture on a single EC2 instance with comprehensive observability using OpenTelemetry and Coralogix monitoring.

## Architecture Overview

The solution includes:

- **4 Distributed Services** in different programming languages:
  - Node.js (Express) - Port 3000
  - Python (Flask) - Port 5000  
  - Java (Spring Boot) - Port 8080
  - Go (Gin) - Port 8090

- **OpenTelemetry Integration**:
  - Auto-instrumentation for all services
  - OTEL Collector with Coralogix spanmetrics
  - Traces, metrics, and logs collection
  - Infrastructure monitoring

- **AWS Services**:
  - EC2 instance with proper IAM roles
  - CloudWatch Agent for AWS metrics/logs
  - SSM Agent for Session Manager access
  - Security groups with appropriate access

## Prerequisites

1. **AWS CLI** installed and configured
2. **AWS Account** with appropriate permissions
3. **Coralogix Account** with Private Key
4. **EC2 Key Pair** in your target region
5. **VPC and Subnet** where the instance will be deployed

## Quick Start

### 1. Clone or Download Files

Ensure you have these files in your working directory:
- `ec2.yaml` - CloudFormation template
- `deploy.sh` - Deployment script
- `delete.sh` - Deletion script
- `main-setup.sh` - Main setup orchestrator
- `setup-*.sh` - Individual service setup scripts
- `README.md` - This documentation

### 2. Make Scripts Executable

```bash
chmod +x deploy.sh delete.sh
```

### 3. Deploy the Stack

```bash
./deploy.sh
```

The script will prompt you for:
- VPC ID
- Subnet ID  
- EC2 Key Pair Name
- Instance Type (default: t3.large)
- Coralogix Private Key
- Coralogix Application Name (default: distributed-services-demo)
- Coralogix Subsystem Name (default: ec2-services)

### 4. Wait for Deployment

The deployment process takes approximately 10-15 minutes:
1. CloudFormation stack creation (2-3 minutes)
2. EC2 instance launch and software installation (5-8 minutes)
3. Service startup and health checks (2-4 minutes)

## Service Endpoints

Once deployed, you can access the services at:

- **Node.js Service**: `http://<PUBLIC_IP>:3000/health`
- **Python Service**: `http://<PUBLIC_IP>:5000/health`
- **Java Service**: `http://<PUBLIC_IP>:8080/health`
- **Go Service**: `http://<PUBLIC_IP>:8090/health`

### API Endpoints with Diverse Response Codes

Each service provides multiple endpoints that generate various HTTP status codes for realistic telemetry:

#### Node.js Service (Port 3000)
- `GET /health` - Health check (200 OK)
- `GET /api/data` - Calls Python service (200 OK, 503 on error)
- `GET /api/orders/{orderId}` - Order lookup (200, 400, 401, 404)
- `GET /api/timeout` - Slow operation (200, 500)
- `POST /api/process` - Data processing (201, 422, 500)

#### Python Service (Port 5000)
- `GET /health` - Health check (200 OK)
- `GET /api/data` - Calls Java service (200 OK, 503/504 on error)
- `GET /api/products/{productId}` - Product lookup (200, 400, 404, 410)
- `POST /api/calculate` - Math operations (200, 400, 422, 500)
- `GET /api/heavy` - CPU-intensive operation (200, 500)

#### Java Service (Port 8080)
- `GET /health` - Health check (200 OK)
- `GET /api/data` - Calls Go service (200 OK, 503 on error)
- `GET /api/users/{userId}` - User lookup (200, 400, 403, 404)
- `GET /api/slow` - Slow processing (200, 500)

#### Go Service (Port 8090)
- `GET /health` - Health check (200 OK)
- `GET /api/data` - Calls Node.js service (200 OK, 503 on error)
- `GET /api/inventory/{itemId}` - Inventory lookup (200, 400, 403, 404)
- `POST /api/validate` - Data validation (200, 400, 422, 500)
- `GET /api/stress` - Stress test (200, 500)

### Error Simulation
Add `?simulate_error=true` to any `/api/data` endpoint to force error responses for testing.

## Monitoring and Observability

### OpenTelemetry Collector

The OTEL Collector is configured to:
- Collect traces from all services via OTLP (gRPC on port 4317)
- Generate span metrics using the spanmetrics processor
- Collect host metrics (CPU, memory, disk, network)
- **Collect structured logs** from systemd journal and log files
- **Correlate logs with traces** using request IDs
- Forward all telemetry data to Coralogix AP1 region

### Structured Logging

Each service generates **structured JSON logs** that include:
- **timestamp**: ISO 8601 timestamp
- **service**: Service name (nodejs, python, java, go)
- **method**: HTTP method (GET, POST, etc.)
- **path**: Request path
- **status_code**: HTTP status code (200, 404, 500, etc.)
- **request_id**: Unique request identifier for correlation
- **duration_ms**: Request processing time in milliseconds
- **error**: Error message (if applicable)
- **level**: Log level (INFO, WARN, ERROR) based on status code

### Log-Trace Correlation

- **1:1 Correlation**: Each HTTP request generates exactly one log entry and one trace
- **Request ID Matching**: Logs and traces share the same request_id for correlation
- **Status Code Consistency**: Log status_code matches HTTP response status and trace status
- **Timing Correlation**: Log duration_ms matches trace span duration

### Coralogix Integration

Data is sent to Coralogix with:
- **Domain**: `coralogix.ap-southeast-1.coralogix.com`
- **Application Name**: Configurable (default: distributed-services-demo)
- **Subsystem Name**: Configurable (default: ec2-services)

### CloudWatch Integration

- **CloudWatch Agent** collects EC2 metrics and logs
- **Custom namespace**: `DistributedServices/EC2`
- **Log Groups**: 
  - `/aws/ec2/distributed-services/system`
  - `/aws/ec2/distributed-services/applications`

## Management and Access

### SSM Session Manager

Connect to the instance without SSH:
```bash
aws ssm start-session --target <INSTANCE_ID> --region <REGION>
```

### Health Check Script

On the instance, run:
```bash
sudo /opt/distributed-services/health-check.sh
```

### Traffic Generation for Testing

To generate diverse HTTP traffic with various status codes for telemetry testing:
```bash
sudo /opt/distributed-services/generate-traffic.sh
```

This script generates:
- **50%** successful requests (200 OK)
- **25%** error requests (4xx, 5xx)
- **10%** slow requests (timeouts, heavy processing)
- **10%** POST requests with various payloads
- **5%** explicit error simulation
- Periodic traffic bursts for load testing

### Log-Trace Correlation Verification

To verify that logs are generated for each HTTP status code and correlate with traces:
```bash
sudo /opt/distributed-services/verify-log-trace-correlation.sh
```

This script:
- **Tests all endpoints** with various status codes
- **Verifies structured JSON logs** are generated
- **Correlates logs with HTTP responses** using request IDs
- **Validates log structure** (timestamp, service, status_code, level)
- **Shows statistics** of logs by status code
- **Confirms OTEL collector** is processing logs

### Service Management

Services are managed via systemd:
```bash
# Check service status
sudo systemctl status nodejs-service
sudo systemctl status python-service  
sudo systemctl status java-service
sudo systemctl status go-service
sudo systemctl status otel-collector

# Restart services
sudo systemctl restart <service-name>

# View logs
sudo journalctl -u <service-name> -f
```

## Configuration Details

### OpenTelemetry Environment Variables

All services use these OTEL environment variables:
- `OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4317`
- `OTEL_EXPORTER_OTLP_PROTOCOL=grpc`
- `OTEL_SERVICE_NAME=distributed-services`
- `OTEL_RESOURCE_ATTRIBUTES=service.name=distributed-services,service.version=1.0.0`

### Service Dependencies

Services have inter-dependencies for demonstration:
- Node.js → Python
- Python → Java  
- Java → Go
- Go → Node.js

This creates a distributed trace across all services when calling `/api/data` endpoints.

## Troubleshooting

### Common Issues

1. **Services not starting**: Check systemd logs
   ```bash
   sudo journalctl -u <service-name> -n 50
   ```

2. **OTEL Collector issues**: Check collector logs
   ```bash
   sudo journalctl -u otel-collector -n 50
   ```

3. **Network connectivity**: Verify security group rules

4. **Coralogix connection**: Verify private key and region settings

### Log Locations

- **Application logs**: `/opt/distributed-services/*/logs/`
- **System logs**: `/var/log/messages`
- **Setup logs**: `/var/log/setup-complete.log`
- **Health check**: `/var/log/initial-health-check.log`

## Cleanup

To delete all resources:

```bash
./delete.sh
```

This will:
1. Confirm deletion with user prompts
2. Delete the CloudFormation stack
3. Remove all associated AWS resources
4. Provide deletion summary

## Cost Considerations

Estimated monthly costs (us-east-1):
- **t3.large EC2 instance**: ~$60-70/month
- **CloudWatch logs/metrics**: ~$5-15/month
- **Data transfer**: Varies based on usage

## Security Features

- **IAM roles** with least privilege access
- **Security groups** with minimal required ports
- **SSM Session Manager** for secure access
- **No SSH keys required** for management

## Customization

### Changing Instance Type

Modify the `InstanceType` parameter in the deployment script or CloudFormation template.

### Adding More Services

1. Create a new `setup-yourservice.sh` script following the existing patterns
2. Add the script call to `main-setup.sh`
3. Update security group rules in `ec2.yaml` if needed
4. Add health checks to the monitoring script

### Hosting Setup Scripts

For production use, consider hosting the setup scripts on:
- **S3 Bucket**: Upload scripts and update the CloudFormation template URLs
- **GitHub Repository**: Use raw GitHub URLs in the template
- **Private Repository**: Use authenticated downloads with proper IAM roles

### Different Regions

Update the `REGION` variable in both deployment scripts and ensure:
- AMI ID is valid for the target region
- Coralogix domain matches your region
- VPC/Subnet are in the correct region

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review CloudFormation events in AWS Console
3. Check service logs on the EC2 instance
4. Verify Coralogix configuration and connectivity
