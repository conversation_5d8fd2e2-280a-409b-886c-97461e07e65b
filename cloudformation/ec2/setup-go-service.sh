#!/bin/bash
set -e

echo "Setting up Go service..."

# Create Go service directory
mkdir -p /opt/distributed-services/go-service
cd /opt/distributed-services/go-service

# Create go.mod
cat > go.mod << 'EOF'
module go-service

go 1.21

require (
    github.com/gin-gonic/gin v1.9.1
    go.opentelemetry.io/otel v1.21.0
    go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc v1.21.0
    go.opentelemetry.io/otel/sdk v1.21.0
    go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin v0.46.1
)
EOF

# Create Go application
cat > main.go << 'EOF'
package main

import (
    "context"
    "encoding/json"
    "fmt"
    "log"
    "math/rand"
    "net/http"
    "os"
    "strconv"
    "sync/atomic"
    "time"

    "github.com/gin-gonic/gin"
    "go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
    "go.opentelemetry.io/otel"
    "go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
    "go.opentelemetry.io/otel/sdk/resource"
    "go.opentelemetry.io/otel/sdk/trace"
    semconv "go.opentelemetry.io/otel/semconv/v1.21.0"
)

var requestCounter int64

// LogEntry represents a structured log entry
type LogEntry struct {
    Timestamp   string  `json:"timestamp"`
    Service     string  `json:"service"`
    Method      string  `json:"method"`
    Path        string  `json:"path"`
    StatusCode  int     `json:"status_code"`
    RequestID   string  `json:"request_id"`
    DurationMs  float64 `json:"duration_ms,omitempty"`
    Error       string  `json:"error,omitempty"`
    Level       string  `json:"level"`
}

// logRequest logs a structured request entry
func logRequest(method, path string, statusCode int, requestID string, duration time.Duration, err error) {
    var errorMsg string
    if err != nil {
        errorMsg = err.Error()
    }

    level := "INFO"
    if statusCode >= 500 {
        level = "ERROR"
    } else if statusCode >= 400 {
        level = "WARN"
    }

    logEntry := LogEntry{
        Timestamp:  time.Now().Format(time.RFC3339),
        Service:    "go",
        Method:     method,
        Path:       path,
        StatusCode: statusCode,
        RequestID:  requestID,
        DurationMs: float64(duration.Nanoseconds()) / 1e6,
        Error:      errorMsg,
        Level:      level,
    }

    if logJSON, err := json.Marshal(logEntry); err == nil {
        fmt.Println(string(logJSON))
    } else {
        log.Printf("Failed to marshal log entry: %v", err)
    }
}

func initTracer() func() {
    ctx := context.Background()

    exporter, err := otlptracegrpc.New(ctx,
        otlptracegrpc.WithEndpoint("http://localhost:4317"),
        otlptracegrpc.WithInsecure(),
    )
    if err != nil {
        log.Fatal("Failed to create OTLP trace exporter: ", err)
    }

    res, err := resource.New(ctx,
        resource.WithAttributes(
            semconv.ServiceName("go-service"),
            semconv.ServiceVersion("1.0.0"),
        ),
    )
    if err != nil {
        log.Fatal("Failed to create resource: ", err)
    }

    tp := trace.NewTracerProvider(
        trace.WithBatcher(exporter),
        trace.WithResource(res),
    )

    otel.SetTracerProvider(tp)

    return func() {
        if err := tp.Shutdown(ctx); err != nil {
            log.Printf("Error shutting down tracer provider: %v", err)
        }
    }
}

// Middleware to simulate random delays
func randomDelayMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        if rand.Float32() < 0.07 { // 7% chance of delay
            delay := time.Duration(rand.Intn(1500)+500) * time.Millisecond // 500-2000ms
            time.Sleep(delay)
        }
        c.Next()
    }
}

func main() {
    cleanup := initTracer()
    defer cleanup()

    rand.Seed(time.Now().UnixNano())

    r := gin.Default()
    r.Use(otelgin.Middleware("go-service"))
    r.Use(randomDelayMiddleware())

    r.GET("/health", func(c *gin.Context) {
        startTime := time.Now()
        response := gin.H{
            "service":            "go",
            "status":             "healthy",
            "timestamp":          time.Now().Format(time.RFC3339),
            "requests_processed": atomic.LoadInt64(&requestCounter),
        }
        duration := time.Since(startTime)
        logRequest("GET", "/health", http.StatusOK, "health-check", duration, nil)
        c.JSON(http.StatusOK, response)
    })

    r.GET("/api/data", func(c *gin.Context) {
        startTime := time.Now()
        reqID := fmt.Sprintf("go-%d", atomic.AddInt64(&requestCounter, 1))

        // Simulate various response scenarios
        simulateError := c.Query("simulate_error") == "true" || rand.Float32() < 0.11 // 11% chance of error

        if simulateError {
            simulateErrorResponse(c, reqID, startTime)
            return
        }

        // Call Node.js service
        client := &http.Client{Timeout: 5 * time.Second}
        resp, err := client.Get("http://localhost:3000/health")
        if err != nil {
            duration := time.Since(startTime)
            errorMsg := "Failed to call Node.js service: " + err.Error()
            logRequest("GET", "/api/data", http.StatusServiceUnavailable, reqID, duration, err)
            c.JSON(http.StatusServiceUnavailable, gin.H{
                "service":    "go",
                "error":      errorMsg,
                "timestamp":  time.Now().Format(time.RFC3339),
                "request_id": reqID,
            })
            return
        }
        defer resp.Body.Close()

        var nodeResponse map[string]interface{}
        json.NewDecoder(resp.Body).Decode(&nodeResponse)

        duration := time.Since(startTime)
        response := gin.H{
            "service":      "go",
            "data":         "Hello from Go service",
            "node_service": nodeResponse,
            "timestamp":    time.Now().Format(time.RFC3339),
            "request_id":   reqID,
        }

        logRequest("GET", "/api/data", http.StatusOK, reqID, duration, nil)
        c.JSON(http.StatusOK, response)
    })

    r.GET("/api/inventory/:itemId", func(c *gin.Context) {
        reqID := fmt.Sprintf("go-%d", atomic.AddInt64(&requestCounter, 1))
        itemID := c.Param("itemId")

        // Simulate inventory validation
        if itemID == "" {
            c.JSON(http.StatusBadRequest, gin.H{
                "service":    "go",
                "error":      "Item ID is required",
                "timestamp":  time.Now().Format(time.RFC3339),
                "request_id": reqID,
            })
            return
        }

        if itemID == "invalid" {
            c.JSON(http.StatusBadRequest, gin.H{
                "service":    "go",
                "error":      "Invalid item ID format",
                "timestamp":  time.Now().Format(time.RFC3339),
                "request_id": reqID,
            })
            return
        }

        if itemID == "notfound" {
            c.JSON(http.StatusNotFound, gin.H{
                "service":    "go",
                "error":      "Item not found in inventory",
                "timestamp":  time.Now().Format(time.RFC3339),
                "request_id": reqID,
            })
            return
        }

        if itemID == "restricted" {
            c.JSON(http.StatusForbidden, gin.H{
                "service":    "go",
                "error":      "Access to restricted item denied",
                "timestamp":  time.Now().Format(time.RFC3339),
                "request_id": reqID,
            })
            return
        }

        // Simulate successful inventory data
        c.JSON(http.StatusOK, gin.H{
            "service":     "go",
            "item_id":     itemID,
            "name":        fmt.Sprintf("Item %s", itemID),
            "quantity":    rand.Intn(100) + 1,
            "location":    fmt.Sprintf("Warehouse-%c", 'A'+rand.Intn(5)),
            "last_updated": time.Now().Add(-time.Duration(rand.Intn(24)) * time.Hour).Format(time.RFC3339),
            "timestamp":   time.Now().Format(time.RFC3339),
            "request_id":  reqID,
        })
    })

    r.POST("/api/validate", func(c *gin.Context) {
        reqID := fmt.Sprintf("go-%d", atomic.AddInt64(&requestCounter, 1))

        var requestData map[string]interface{}
        if err := c.ShouldBindJSON(&requestData); err != nil {
            c.JSON(http.StatusBadRequest, gin.H{
                "service":    "go",
                "error":      "Invalid JSON format",
                "timestamp":  time.Now().Format(time.RFC3339),
                "request_id": reqID,
            })
            return
        }

        // Simulate validation logic
        if requestData["type"] == nil {
            c.JSON(http.StatusUnprocessableEntity, gin.H{
                "service":    "go",
                "error":      "Missing required field: type",
                "timestamp":  time.Now().Format(time.RFC3339),
                "request_id": reqID,
            })
            return
        }

        if requestData["type"] == "invalid" {
            c.JSON(http.StatusBadRequest, gin.H{
                "service":    "go",
                "error":      "Invalid type specified",
                "timestamp":  time.Now().Format(time.RFC3339),
                "request_id": reqID,
            })
            return
        }

        // Random processing failure
        if rand.Float32() < 0.15 { // 15% chance of processing failure
            c.JSON(http.StatusInternalServerError, gin.H{
                "service":    "go",
                "error":      "Validation processing failed",
                "timestamp":  time.Now().Format(time.RFC3339),
                "request_id": reqID,
            })
            return
        }

        c.JSON(http.StatusOK, gin.H{
            "service":       "go",
            "validation":    "passed",
            "processed_data": requestData,
            "timestamp":     time.Now().Format(time.RFC3339),
            "request_id":    reqID,
        })
    })

    r.GET("/api/stress", func(c *gin.Context) {
        reqID := fmt.Sprintf("go-%d", atomic.AddInt64(&requestCounter, 1))

        // Simulate CPU-intensive operation
        iterations := rand.Intn(1000000) + 500000
        start := time.Now()

        // Busy work
        sum := 0
        for i := 0; i < iterations; i++ {
            sum += i
        }

        processingTime := time.Since(start)

        // Random chance of failure during stress test
        if rand.Float32() < 0.25 { // 25% chance of failure
            c.JSON(http.StatusInternalServerError, gin.H{
                "service":         "go",
                "error":           "Stress test failed due to resource exhaustion",
                "processing_time": processingTime.String(),
                "timestamp":       time.Now().Format(time.RFC3339),
                "request_id":      reqID,
            })
            return
        }

        c.JSON(http.StatusOK, gin.H{
            "service":         "go",
            "data":            "Stress test completed",
            "iterations":      iterations,
            "result":          sum,
            "processing_time": processingTime.String(),
            "timestamp":       time.Now().Format(time.RFC3339),
            "request_id":      reqID,
        })
    })

    fmt.Println("Go service listening at http://localhost:8090")
    r.Run(":8090")
}

func simulateErrorResponse(c *gin.Context, reqID string, startTime time.Time) {
    errorScenarios := []struct {
        status int
        error  string
    }{
        {http.StatusBadRequest, "Bad request: Invalid request format"},
        {http.StatusUnauthorized, "Unauthorized: Authentication token expired"},
        {http.StatusForbidden, "Forbidden: Insufficient privileges"},
        {http.StatusRequestTimeout, "Request timeout: Operation exceeded time limit"},
        {http.StatusTooManyRequests, "Too many requests: Rate limiting active"},
        {http.StatusInternalServerError, "Internal server error: Memory allocation failed"},
        {http.StatusBadGateway, "Bad gateway: Downstream service error"},
        {http.StatusServiceUnavailable, "Service unavailable: Circuit breaker open"},
        {http.StatusGatewayTimeout, "Gateway timeout: Upstream service timeout"},
    }

    scenario := errorScenarios[rand.Intn(len(errorScenarios))]
    duration := time.Since(startTime)

    logRequest("GET", "/api/data", scenario.status, reqID, duration, fmt.Errorf(scenario.error))

    c.JSON(scenario.status, gin.H{
        "service":    "go",
        "error":      scenario.error,
        "timestamp":  time.Now().Format(time.RFC3339),
        "request_id": reqID,
    })
}
EOF

# Build Go service
go mod tidy
go build -o go-service main.go

# Create systemd service
cat > /etc/systemd/system/go-service.service << 'EOF'
[Unit]
Description=Go Service
After=network.target otel-collector.service
Requires=otel-collector.service

[Service]
Type=simple
User=ec2-user
WorkingDirectory=/opt/distributed-services/go-service
ExecStart=/opt/distributed-services/go-service/go-service
Restart=always
RestartSec=5
EnvironmentFile=/opt/distributed-services/.env
StandardOutput=journal
StandardError=journal
SyslogIdentifier=go-service

[Install]
WantedBy=multi-user.target
EOF

echo "Go service setup completed"
