#!/bin/bash

# Log and Trace Correlation Verification Script
# This script verifies that logs are generated for each HTTP status code and correlate with traces

echo "=== Log and Trace Correlation Verification ==="
echo "Timestamp: $(date)"
echo

# Function to make a request and capture response
make_test_request() {
    local url=$1
    local expected_status=$2
    local description=$3
    
    echo "Testing: $description"
    echo "URL: $url"
    
    # Make request and capture status code
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$url")
    http_code=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//g')
    
    echo "Expected Status: $expected_status"
    echo "Actual Status: $http_code"
    
    if [ "$http_code" = "$expected_status" ]; then
        echo "✓ Status code matches"
    else
        echo "✗ Status code mismatch"
    fi
    
    # Extract request ID from response if available
    request_id=$(echo "$body" | jq -r '.request_id // empty' 2>/dev/null)
    if [ -n "$request_id" ]; then
        echo "Request ID: $request_id"
        
        # Wait a moment for logs to be written
        sleep 2
        
        # Check if log entry exists with matching status code
        log_count=$(journalctl -u nodejs-service -u python-service -u java-service -u go-service --since "1 minute ago" -o json | \
                   jq -r --arg req_id "$request_id" --arg status "$http_code" \
                   'select(.MESSAGE | contains($req_id) and contains($status)) | .MESSAGE' | wc -l)
        
        if [ "$log_count" -gt 0 ]; then
            echo "✓ Found $log_count matching log entries"
        else
            echo "✗ No matching log entries found"
        fi
    else
        echo "⚠ No request ID in response"
    fi
    
    echo "---"
}

# Function to test JSON log parsing
test_json_logs() {
    echo "=== Testing JSON Log Structure ==="
    
    # Get recent logs from all services
    echo "Checking structured logs from services..."
    
    services=("nodejs-service" "python-service" "java-service" "go-service")
    
    for service in "${services[@]}"; do
        echo "Checking $service logs..."
        
        # Get recent JSON logs
        json_logs=$(journalctl -u "$service" --since "5 minutes ago" -o json | \
                   jq -r 'select(.MESSAGE | test("^\\{.*\\}$")) | .MESSAGE' | \
                   head -5)
        
        if [ -n "$json_logs" ]; then
            echo "✓ Found structured JSON logs for $service"
            
            # Validate JSON structure
            echo "$json_logs" | while read -r log_line; do
                if echo "$log_line" | jq -e '.timestamp and .service and .status_code and .level' > /dev/null 2>&1; then
                    status_code=$(echo "$log_line" | jq -r '.status_code')
                    level=$(echo "$log_line" | jq -r '.level')
                    echo "  ✓ Valid log: Status $status_code, Level $level"
                else
                    echo "  ✗ Invalid log structure: $log_line"
                fi
            done
        else
            echo "✗ No structured JSON logs found for $service"
        fi
        echo
    done
}

# Function to generate test traffic and verify correlation
generate_and_verify() {
    echo "=== Generating Test Traffic and Verifying Correlation ==="
    
    # Test successful requests (200 OK)
    make_test_request "http://localhost:3000/health" "200" "Node.js Health Check"
    make_test_request "http://localhost:5000/health" "200" "Python Health Check"
    make_test_request "http://localhost:8080/health" "200" "Java Health Check"
    make_test_request "http://localhost:8090/health" "200" "Go Health Check"
    
    # Test successful API calls
    make_test_request "http://localhost:3000/api/data" "200" "Node.js API Data"
    make_test_request "http://localhost:5000/api/data" "200" "Python API Data"
    make_test_request "http://localhost:8080/api/data" "200" "Java API Data"
    make_test_request "http://localhost:8090/api/data" "200" "Go API Data"
    
    # Test error scenarios
    make_test_request "http://localhost:3000/api/orders/invalid" "400" "Node.js Bad Request"
    make_test_request "http://localhost:5000/api/products/notfound" "404" "Python Not Found"
    make_test_request "http://localhost:8080/api/users/forbidden" "403" "Java Forbidden"
    make_test_request "http://localhost:8090/api/inventory/restricted" "403" "Go Forbidden"
    
    # Test forced errors
    make_test_request "http://localhost:3000/api/data?simulate_error=true" "4xx|5xx" "Node.js Simulated Error"
    make_test_request "http://localhost:5000/api/data?simulate_error=true" "4xx|5xx" "Python Simulated Error"
    
    # Test POST requests
    echo "Testing POST requests..."
    
    # Node.js POST
    post_response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "http://localhost:3000/api/process")
    post_status=$(echo "$post_response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    echo "Node.js POST Status: $post_status"
    
    # Python POST with valid data
    post_response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST -H "Content-Type: application/json" \
                   -d '{"operation":"add","a":10,"b":5}' "http://localhost:5000/api/calculate")
    post_status=$(echo "$post_response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    echo "Python POST (valid) Status: $post_status"
    
    # Python POST with invalid data (division by zero)
    post_response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST -H "Content-Type: application/json" \
                   -d '{"operation":"divide","a":10,"b":0}' "http://localhost:5000/api/calculate")
    post_status=$(echo "$post_response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    echo "Python POST (invalid) Status: $post_status"
}

# Function to show log statistics
show_log_statistics() {
    echo "=== Log Statistics by Status Code ==="
    
    services=("nodejs-service" "python-service" "java-service" "go-service")
    
    for service in "${services[@]}"; do
        echo "Service: $service"
        
        # Count logs by status code
        journalctl -u "$service" --since "10 minutes ago" -o json | \
        jq -r 'select(.MESSAGE | test("^\\{.*\\}$")) | .MESSAGE' | \
        jq -r '.status_code' | \
        sort | uniq -c | \
        while read count status; do
            echo "  Status $status: $count logs"
        done
        
        echo
    done
}

# Function to verify OTEL collector is receiving logs
verify_otel_logs() {
    echo "=== Verifying OTEL Collector Log Reception ==="
    
    # Check OTEL collector logs for log processing
    otel_logs=$(journalctl -u otel-collector --since "5 minutes ago" | grep -i "log" | tail -10)
    
    if [ -n "$otel_logs" ]; then
        echo "✓ OTEL Collector is processing logs"
        echo "Recent log processing entries:"
        echo "$otel_logs"
    else
        echo "⚠ No recent log processing entries found in OTEL Collector"
    fi
}

# Main execution
main() {
    # Check if services are running
    echo "Checking service status..."
    services=("nodejs-service" "python-service" "java-service" "go-service" "otel-collector")
    
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "$service"; then
            echo "✓ $service is running"
        else
            echo "✗ $service is not running"
            exit 1
        fi
    done
    
    echo
    
    # Run verification tests
    test_json_logs
    generate_and_verify
    show_log_statistics
    verify_otel_logs
    
    echo "=== Verification Complete ==="
    echo "Check Coralogix dashboard for correlated traces and logs"
    echo "Logs should show the same status codes as the HTTP responses"
    echo "Each trace should have a corresponding log entry with matching request_id"
}

# Check if jq is available
if ! command -v jq &> /dev/null; then
    echo "Installing jq for JSON processing..."
    yum install -y jq
fi

# Run main function
main "$@"
