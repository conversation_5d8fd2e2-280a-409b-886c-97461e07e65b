#!/bin/bash

# Distributed Services CloudFormation Stack Deletion Script
# This script safely deletes the CloudFormation stack and all associated resources

set -e

# Configuration
STACK_NAME="distributed-services-stack"
REGION="us-east-1"  # Change this to match your deployment region

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if AWS CLI is installed and configured
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi
    
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI is not configured. Please run 'aws configure' first."
        exit 1
    fi
    
    print_success "AWS CLI is properly configured"
}

# Function to check if stack exists
check_stack_exists() {
    print_status "Checking if stack exists..."
    
    if aws cloudformation describe-stacks --stack-name $STACK_NAME --region $REGION &> /dev/null; then
        print_success "Stack '$STACK_NAME' found"
        return 0
    else
        print_warning "Stack '$STACK_NAME' does not exist or has already been deleted"
        return 1
    fi
}

# Function to get stack information before deletion
get_stack_info() {
    print_status "Retrieving stack information..."
    
    STACK_STATUS=$(aws cloudformation describe-stacks \
        --stack-name $STACK_NAME \
        --region $REGION \
        --query 'Stacks[0].StackStatus' \
        --output text)
    
    INSTANCE_ID=$(aws cloudformation describe-stacks \
        --stack-name $STACK_NAME \
        --region $REGION \
        --query 'Stacks[0].Outputs[?OutputKey==`InstanceId`].OutputValue' \
        --output text 2>/dev/null || echo "N/A")
    
    PUBLIC_IP=$(aws cloudformation describe-stacks \
        --stack-name $STACK_NAME \
        --region $REGION \
        --query 'Stacks[0].Outputs[?OutputKey==`InstancePublicIP`].OutputValue' \
        --output text 2>/dev/null || echo "N/A")
    
    echo
    print_status "Stack Information:"
    echo "  Stack Name: $STACK_NAME"
    echo "  Stack Status: $STACK_STATUS"
    echo "  Instance ID: $INSTANCE_ID"
    echo "  Public IP: $PUBLIC_IP"
    echo "  Region: $REGION"
}

# Function to confirm deletion
confirm_deletion() {
    echo
    print_warning "This will permanently delete the following resources:"
    echo "  - EC2 Instance and all data on it"
    echo "  - Security Group"
    echo "  - IAM Role and Instance Profile"
    echo "  - All distributed services and their data"
    echo "  - CloudWatch logs (if configured for deletion)"
    echo
    
    read -p "Are you sure you want to delete the stack? (yes/no): " CONFIRM
    
    if [[ $CONFIRM != "yes" ]]; then
        print_status "Deletion cancelled by user"
        exit 0
    fi
    
    echo
    read -p "Type the stack name '$STACK_NAME' to confirm: " CONFIRM_STACK_NAME
    
    if [[ $CONFIRM_STACK_NAME != $STACK_NAME ]]; then
        print_error "Stack name confirmation failed. Deletion cancelled."
        exit 1
    fi
}

# Function to delete the stack
delete_stack() {
    print_status "Initiating stack deletion..."
    
    aws cloudformation delete-stack \
        --stack-name $STACK_NAME \
        --region $REGION
    
    if [ $? -eq 0 ]; then
        print_success "Stack deletion initiated successfully"
    else
        print_error "Failed to initiate stack deletion"
        exit 1
    fi
}

# Function to wait for stack deletion to complete
wait_for_deletion() {
    print_status "Waiting for stack deletion to complete..."
    print_status "This may take several minutes..."
    
    # Wait for stack deletion
    aws cloudformation wait stack-delete-complete \
        --stack-name $STACK_NAME \
        --region $REGION
    
    if [ $? -eq 0 ]; then
        print_success "Stack deletion completed successfully!"
    else
        print_error "Stack deletion failed or timed out"
        
        # Check current stack status
        CURRENT_STATUS=$(aws cloudformation describe-stacks \
            --stack-name $STACK_NAME \
            --region $REGION \
            --query 'Stacks[0].StackStatus' \
            --output text 2>/dev/null || echo "STACK_NOT_FOUND")
        
        if [[ $CURRENT_STATUS == "STACK_NOT_FOUND" ]]; then
            print_success "Stack has been deleted successfully"
        else
            print_error "Current stack status: $CURRENT_STATUS"
            print_error "Please check the CloudFormation console for more details"
            exit 1
        fi
    fi
}

# Function to clean up any remaining resources (optional)
cleanup_remaining_resources() {
    print_status "Checking for any remaining resources..."
    
    # Note: CloudFormation should handle all resource cleanup
    # This function is here for any manual cleanup if needed
    
    print_success "Resource cleanup check completed"
}

# Function to show deletion summary
show_deletion_summary() {
    echo
    print_success "Stack Deletion Summary:"
    echo "  Stack Name: $STACK_NAME"
    echo "  Region: $REGION"
    echo "  Status: DELETED"
    echo "  Timestamp: $(date)"
    echo
    print_status "All resources have been successfully removed."
    echo
    print_status "Note: CloudWatch logs may be retained based on their retention policy."
    print_status "Check the CloudWatch console if you need to manually delete log groups."
}

# Main execution
main() {
    echo "=========================================="
    echo "Distributed Services Stack Deletion"
    echo "=========================================="
    echo
    
    check_aws_cli
    
    if ! check_stack_exists; then
        exit 0
    fi
    
    get_stack_info
    confirm_deletion
    delete_stack
    wait_for_deletion
    cleanup_remaining_resources
    show_deletion_summary
    
    echo
    print_success "Stack deletion completed successfully!"
}

# Handle script interruption
trap 'print_error "Script interrupted. Stack deletion may still be in progress."; exit 1' INT TERM

# Run main function
main "$@"
