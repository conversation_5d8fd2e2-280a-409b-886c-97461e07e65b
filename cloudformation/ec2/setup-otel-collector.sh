#!/bin/bash
set -e

echo "Setting up OpenTelemetry Collector..."

cd /opt/distributed-services

# Download and install OpenTelemetry Collector Contrib
wget https://github.com/open-telemetry/opentelemetry-collector-releases/releases/latest/download/otelcol-contrib_linux_amd64.tar.gz
tar -xzf otelcol-contrib_linux_amd64.tar.gz
mv otelcol-contrib /usr/local/bin/

# Create OTEL Collector configuration
cat > otel-collector-config.yaml << 'EOF'
receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318
  hostmetrics:
    collection_interval: 30s
    scrapers:
      cpu:
        metrics:
          system.cpu.utilization:
            enabled: true
      memory:
        metrics:
          system.memory.utilization:
            enabled: true
      disk:
        metrics:
          system.disk.io.time:
            enabled: true
      network:
        metrics:
          system.network.io:
            enabled: true
      filesystem:
        metrics:
          system.filesystem.utilization:
            enabled: true
  filelog:
    include:
      - /var/log/messages
      - /var/log/secure
      - /opt/distributed-services/*/logs/*.log
    operators:
      - type: json_parser
        id: json_parser
        if: 'body matches "^\\{"'
        parse_from: attributes.log
        parse_to: attributes

  # Add journald receiver for systemd service logs
  journald:
    directory: /var/log/journal
    units:
      - nodejs-service
      - python-service
      - java-service
      - go-service
      - otel-collector
    operators:
      - type: json_parser
        id: journald_json_parser
        if: 'body matches "^\\{"'
        parse_from: body
        parse_to: attributes

processors:
  batch:
    timeout: 1s
    send_batch_size: 1024
  memory_limiter:
    limit_mib: 512
  spanmetrics:
    metrics_exporter: coralogix
    latency_histogram_buckets: [100us, 1ms, 2ms, 6ms, 10ms, 100ms, 250ms]
    dimensions_cache_size: 1000
    aggregation_temporality: "AGGREGATION_TEMPORALITY_CUMULATIVE"

exporters:
  coralogix:
    domain: "coralogix.ap-southeast-1.coralogix.com"
    private_key: "${CORALOGIX_PRIVATE_KEY}"
    application_name: "${CORALOGIX_APPLICATION_NAME}"
    subsystem_name: "${CORALOGIX_SUBSYSTEM_NAME}"
    timeout: 30s
  logging:
    loglevel: debug

service:
  pipelines:
    traces:
      receivers: [otlp]
      processors: [memory_limiter, batch, spanmetrics]
      exporters: [coralogix, logging]
    metrics:
      receivers: [otlp, hostmetrics]
      processors: [memory_limiter, batch]
      exporters: [coralogix, logging]
    logs:
      receivers: [otlp, filelog, journald]
      processors: [memory_limiter, batch]
      exporters: [coralogix, logging]
EOF

# Create systemd service for OTEL Collector
cat > /etc/systemd/system/otel-collector.service << 'EOF'
[Unit]
Description=OpenTelemetry Collector
After=network.target

[Service]
Type=simple
User=root
ExecStart=/usr/local/bin/otelcol-contrib --config=/opt/distributed-services/otel-collector-config.yaml
Restart=always
RestartSec=5
EnvironmentFile=/opt/distributed-services/.env

[Install]
WantedBy=multi-user.target
EOF

echo "OpenTelemetry Collector setup completed"
