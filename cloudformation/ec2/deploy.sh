#!/bin/bash

# Distributed Services CloudFormation Stack Deployment Script
# This script deploys the EC2 instance with distributed services and OpenTelemetry monitoring

set -e

# Configuration
STACK_NAME="distributed-services-stack"
TEMPLATE_FILE="ec2.yaml"
REGION="us-east-1"  # Change this to your preferred region

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if AWS CLI is installed and configured
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi
    
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI is not configured. Please run 'aws configure' first."
        exit 1
    fi
    
    print_success "AWS CLI is properly configured"
}

# Function to validate CloudFormation template
validate_template() {
    print_status "Validating CloudFormation template..."
    
    if aws cloudformation validate-template --template-body file://$TEMPLATE_FILE --region $REGION > /dev/null; then
        print_success "Template validation passed"
    else
        print_error "Template validation failed"
        exit 1
    fi
}

# Function to get user inputs
get_user_inputs() {
    echo
    print_status "Please provide the following parameters:"
    
    # Get VPC ID
    echo -n "Enter VPC ID: "
    read VPC_ID
    if [[ -z "$VPC_ID" ]]; then
        print_error "VPC ID is required"
        exit 1
    fi
    
    # Get Subnet ID
    echo -n "Enter Subnet ID: "
    read SUBNET_ID
    if [[ -z "$SUBNET_ID" ]]; then
        print_error "Subnet ID is required"
        exit 1
    fi
    
    # Get Key Pair Name
    echo -n "Enter EC2 Key Pair Name: "
    read KEY_PAIR_NAME
    if [[ -z "$KEY_PAIR_NAME" ]]; then
        print_error "Key Pair Name is required"
        exit 1
    fi
    
    # Get Instance Type (with default)
    echo -n "Enter Instance Type (default: t3.large): "
    read INSTANCE_TYPE
    if [[ -z "$INSTANCE_TYPE" ]]; then
        INSTANCE_TYPE="t3.large"
    fi
    
    # Get Coralogix Private Key
    echo -n "Enter Coralogix Private Key: "
    read -s CORALOGIX_PRIVATE_KEY
    echo
    if [[ -z "$CORALOGIX_PRIVATE_KEY" ]]; then
        print_error "Coralogix Private Key is required"
        exit 1
    fi
    
    # Get Coralogix Application Name (with default)
    echo -n "Enter Coralogix Application Name (default: distributed-services-demo): "
    read CORALOGIX_APP_NAME
    if [[ -z "$CORALOGIX_APP_NAME" ]]; then
        CORALOGIX_APP_NAME="distributed-services-demo"
    fi
    
    # Get Coralogix Subsystem Name (with default)
    echo -n "Enter Coralogix Subsystem Name (default: ec2-services): "
    read CORALOGIX_SUBSYSTEM_NAME
    if [[ -z "$CORALOGIX_SUBSYSTEM_NAME" ]]; then
        CORALOGIX_SUBSYSTEM_NAME="ec2-services"
    fi
}

# Function to deploy the stack
deploy_stack() {
    print_status "Deploying CloudFormation stack: $STACK_NAME"
    
    aws cloudformation deploy \
        --template-file $TEMPLATE_FILE \
        --stack-name $STACK_NAME \
        --parameter-overrides \
            VpcId=$VPC_ID \
            SubnetId=$SUBNET_ID \
            KeyPairName=$KEY_PAIR_NAME \
            InstanceType=$INSTANCE_TYPE \
            CoralogixPrivateKey=$CORALOGIX_PRIVATE_KEY \
            CoralogixApplicationName=$CORALOGIX_APP_NAME \
            CoralogixSubsystemName=$CORALOGIX_SUBSYSTEM_NAME \
        --capabilities CAPABILITY_NAMED_IAM \
        --region $REGION
    
    if [ $? -eq 0 ]; then
        print_success "Stack deployment completed successfully!"
    else
        print_error "Stack deployment failed!"
        exit 1
    fi
}

# Function to get stack outputs
get_stack_outputs() {
    print_status "Retrieving stack outputs..."
    
    OUTPUTS=$(aws cloudformation describe-stacks \
        --stack-name $STACK_NAME \
        --region $REGION \
        --query 'Stacks[0].Outputs' \
        --output table)
    
    echo
    print_success "Stack Outputs:"
    echo "$OUTPUTS"
    
    # Get specific outputs for easy access
    INSTANCE_ID=$(aws cloudformation describe-stacks \
        --stack-name $STACK_NAME \
        --region $REGION \
        --query 'Stacks[0].Outputs[?OutputKey==`InstanceId`].OutputValue' \
        --output text)
    
    PUBLIC_IP=$(aws cloudformation describe-stacks \
        --stack-name $STACK_NAME \
        --region $REGION \
        --query 'Stacks[0].Outputs[?OutputKey==`InstancePublicIP`].OutputValue' \
        --output text)
    
    echo
    print_success "Quick Access Information:"
    echo "Instance ID: $INSTANCE_ID"
    echo "Public IP: $PUBLIC_IP"
    echo
    echo "Service URLs:"
    echo "  Node.js Service: http://$PUBLIC_IP:3000/health"
    echo "  Python Service:  http://$PUBLIC_IP:5000/health"
    echo "  Java Service:    http://$PUBLIC_IP:8080/health"
    echo "  Go Service:      http://$PUBLIC_IP:8090/health"
    echo
    echo "SSM Session Manager:"
    echo "  aws ssm start-session --target $INSTANCE_ID --region $REGION"
}

# Function to wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready (this may take 5-10 minutes)..."
    
    PUBLIC_IP=$(aws cloudformation describe-stacks \
        --stack-name $STACK_NAME \
        --region $REGION \
        --query 'Stacks[0].Outputs[?OutputKey==`InstancePublicIP`].OutputValue' \
        --output text)
    
    services=("3000" "5000" "8080" "8090")
    max_attempts=60
    attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        all_ready=true
        
        for port in "${services[@]}"; do
            if ! curl -s -f "http://$PUBLIC_IP:$port/health" > /dev/null 2>&1; then
                all_ready=false
                break
            fi
        done
        
        if [ "$all_ready" = true ]; then
            print_success "All services are ready!"
            return 0
        fi
        
        attempt=$((attempt + 1))
        echo -n "."
        sleep 10
    done
    
    print_warning "Services may still be starting up. Check manually using the URLs above."
}

# Main execution
main() {
    echo "=========================================="
    echo "Distributed Services Stack Deployment"
    echo "=========================================="
    echo
    
    check_aws_cli
    validate_template
    get_user_inputs
    deploy_stack
    get_stack_outputs
    wait_for_services
    
    echo
    print_success "Deployment completed! Your distributed services are now running."
    echo
    print_status "Next steps:"
    echo "1. Test the services using the URLs provided above"
    echo "2. Check Coralogix for telemetry data"
    echo "3. Use SSM Session Manager to connect to the instance"
    echo "4. Run the health check: /opt/distributed-services/health-check.sh"
}

# Run main function
main "$@"
