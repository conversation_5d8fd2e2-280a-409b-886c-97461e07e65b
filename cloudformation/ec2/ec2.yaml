AWSTemplateFormatVersion: '2010-09-09'
Description: 'EC2 instance with distributed services, OpenTelemetry instrumentation, and Coralogix monitoring'

Parameters:
  VpcId:
    Type: AWS::EC2::VPC::Id
    Description: VPC ID where the EC2 instance will be launched
    
  SubnetId:
    Type: AWS::EC2::Subnet::Id
    Description: Subnet ID where the EC2 instance will be launched
    
  InstanceType:
    Type: String
    Default: t3.large
    AllowedValues:
      - t3.medium
      - t3.large
      - t3.xlarge
      - t3.2xlarge
      - m5.large
      - m5.xlarge
      - m5.2xlarge
    Description: EC2 instance type
    
  KeyPairName:
    Type: AWS::EC2::KeyPair::KeyName
    Description: EC2 Key Pair for SSH access
    
  CoralogixPrivateKey:
    Type: String
    NoEcho: true
    Description: Coralogix Private Key for telemetry data
    
  CoralogixApplicationName:
    Type: String
    Default: distributed-services-demo
    Description: Application name for Coralogix
    
  CoralogixSubsystemName:
    Type: String
    Default: ec2-services
    Description: Subsystem name for Coralogix

Resources:
  # IAM Role for EC2 instance
  EC2Role:
    Type: AWS::IAM::Role
    Properties:
      RoleName: DistributedServices-EC2Role
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ec2.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore
        - arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy
      Policies:
        - PolicyName: S3Access
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:ListBucket
                Resource: '*'
        - PolicyName: CloudWatchLogs
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:DescribeLogStreams
                  - logs:DescribeLogGroups
                Resource: '*'

  # Instance Profile
  EC2InstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Properties:
      Roles:
        - !Ref EC2Role

  # Security Group
  SecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: DistributedServices-SecurityGroup
      GroupDescription: Security group for distributed services EC2 instance
      VpcId: !Ref VpcId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 22
          ToPort: 22
          CidrIp: 0.0.0.0/0
          Description: SSH access
        - IpProtocol: tcp
          FromPort: 3000
          ToPort: 3000
          CidrIp: 0.0.0.0/0
          Description: Node.js service
        - IpProtocol: tcp
          FromPort: 5000
          ToPort: 5000
          CidrIp: 0.0.0.0/0
          Description: Python service
        - IpProtocol: tcp
          FromPort: 8080
          ToPort: 8080
          CidrIp: 0.0.0.0/0
          Description: Java service
        - IpProtocol: tcp
          FromPort: 8090
          ToPort: 8090
          CidrIp: 0.0.0.0/0
          Description: Go service
        - IpProtocol: tcp
          FromPort: 4317
          ToPort: 4318
          CidrIp: 0.0.0.0/0
          Description: OTEL Collector OTLP
      SecurityGroupEgress:
        - IpProtocol: -1
          CidrIp: 0.0.0.0/0
          Description: All outbound traffic

  # EC2 Instance
  EC2Instance:
    Type: AWS::EC2::Instance
    Properties:
      ImageId: ami-0c02fb55956c7d316  # Amazon Linux 2023 AMI (update as needed)
      InstanceType: !Ref InstanceType
      KeyName: !Ref KeyPairName
      SubnetId: !Ref SubnetId
      SecurityGroupIds:
        - !Ref SecurityGroup
      IamInstanceProfile: !Ref EC2InstanceProfile
      Tags:
        - Key: Name
          Value: DistributedServices-Instance
        - Key: Environment
          Value: demo
      UserData:
        Fn::Base64: 
          Fn::Sub: |
            #!/bin/bash
            set -e
            
            # Update system and install packages
            yum update -y
            yum install -y docker git wget curl unzip java-17-amazon-corretto-headless python3 python3-pip nodejs npm golang
            
            # Start Docker
            systemctl start docker
            systemctl enable docker
            usermod -a -G docker ec2-user
            
            # Create application directory
            mkdir -p /opt/distributed-services
            cd /opt/distributed-services
            
            # Create environment file
            cat > .env << 'ENVEOF'
            OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4317
            OTEL_EXPORTER_OTLP_PROTOCOL=grpc
            OTEL_SERVICE_NAME=distributed-services
            OTEL_RESOURCE_ATTRIBUTES=service.name=distributed-services,service.version=1.0.0
            CORALOGIX_PRIVATE_KEY=${CoralogixPrivateKey}
            CORALOGIX_APPLICATION_NAME=${CoralogixApplicationName}
            CORALOGIX_SUBSYSTEM_NAME=${CoralogixSubsystemName}
            ENVEOF
            
            # Download setup scripts (you can host these on S3 or GitHub)
            # For now, we'll create them inline

            # Download individual setup scripts
            curl -o setup-otel-collector.sh https://raw.githubusercontent.com/your-repo/setup-otel-collector.sh || echo "Using local script"
            curl -o setup-nodejs-service.sh https://raw.githubusercontent.com/your-repo/setup-nodejs-service.sh || echo "Using local script"
            curl -o setup-python-service.sh https://raw.githubusercontent.com/your-repo/setup-python-service.sh || echo "Using local script"
            curl -o setup-java-service.sh https://raw.githubusercontent.com/your-repo/setup-java-service.sh || echo "Using local script"
            curl -o setup-go-service.sh https://raw.githubusercontent.com/your-repo/setup-go-service.sh || echo "Using local script"
            curl -o setup-cloudwatch-agent.sh https://raw.githubusercontent.com/your-repo/setup-cloudwatch-agent.sh || echo "Using local script"
            curl -o main-setup.sh https://raw.githubusercontent.com/your-repo/main-setup.sh || echo "Using local script"

            # Make scripts executable
            chmod +x *.sh

            # Run main setup script
            ./main-setup.sh
            
            echo "Setup completed" > /var/log/setup-complete.log

Outputs:
  InstanceId:
    Description: EC2 Instance ID
    Value: !Ref EC2Instance
    Export:
      Name: DistributedServices-InstanceId
      
  InstancePublicIP:
    Description: Public IP address of the EC2 instance
    Value: !GetAtt EC2Instance.PublicIp
    Export:
      Name: DistributedServices-PublicIP
      
  InstancePrivateIP:
    Description: Private IP address of the EC2 instance
    Value: !GetAtt EC2Instance.PrivateIp
    Export:
      Name: DistributedServices-PrivateIP

  NodeJSServiceURL:
    Description: Node.js service URL
    Value: !Sub 'http://${EC2Instance.PublicIp}:3000'
    Export:
      Name: DistributedServices-NodeJSURL

  PythonServiceURL:
    Description: Python service URL
    Value: !Sub 'http://${EC2Instance.PublicIp}:5000'
    Export:
      Name: DistributedServices-PythonURL

  JavaServiceURL:
    Description: Java service URL
    Value: !Sub 'http://${EC2Instance.PublicIp}:8080'
    Export:
      Name: DistributedServices-JavaURL

  GoServiceURL:
    Description: Go service URL
    Value: !Sub 'http://${EC2Instance.PublicIp}:8090'
    Export:
      Name: DistributedServices-GoURL

  SSMSessionCommand:
    Description: Command to connect via SSM Session Manager
    Value: !Sub 'aws ssm start-session --target ${EC2Instance}'
    Export:
      Name: DistributedServices-SSMCommand
