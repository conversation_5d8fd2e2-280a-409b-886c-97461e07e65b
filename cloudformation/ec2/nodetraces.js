// index.js
// A simple Node.js application with Express.js, auto-instrumented with OpenTelemetry for traces, metrics, and logs with span/trace IDs.
// HTTP server on port 3000. Access paths to generate traces/logs with varying severities, latencies, and outcomes.
// Routes: / (info, quick success), /info (info, quick success), /warn (warn, 500ms delay), /error (error, immediate failure), /slow-error (error, 2000ms delay).
// Run with: node index.js
// Dependencies: npm install express @opentelemetry/api @opentelemetry/sdk-node @opentelemetry/auto-instrumentations-node @opentelemetry/sdk-trace-node @opentelemetry/sdk-metrics
// Note: Uses console exporters for traces/metrics. In production, use OTLP (e.g., Jaeger, Prometheus).

const opentelemetry = require('@opentelemetry/api');
const { NodeSDK } = require('@opentelemetry/sdk-node');
const { getNodeAutoInstrumentations } = require('@opentelemetry/auto-instrumentations-node');
const { OTLPTraceExporter } = require('@opentelemetry/exporter-trace-otlp-http');
const { PeriodicExportingMetricReader, ConsoleMetricExporter } = require('@opentelemetry/sdk-metrics');
const { diag, DiagConsoleLogger, DiagLogLevel } = require('@opentelemetry/api');

// Set up OTel diagnostic logging
diag.setLogger(new DiagConsoleLogger(), DiagLogLevel.INFO);

// Configure OTel SDK
const sdk = new NodeSDK({
  traceExporter: new OTLPTraceExporter({
    // url: process.env.OTEL_EXPORTER_OTLP_ENDPOINT || 'http://localhost:4317',
    url: 'https://ingress.ap1.coralogix.com:443/v1/traces',
  headers: {
    Authorization: 'Bearer cxtp_Qd7ke2dhCmpybJuGZxBn5EYeuXUjlT',
  },
  }),
  metricReaders: [new PeriodicExportingMetricReader({
    exporter: new ConsoleMetricExporter(),
    exportIntervalMillis: 5000,
  })],
  instrumentations: [getNodeAutoInstrumentations()],
});

// Start SDK
try {
  sdk.start();
  console.log('OpenTelemetry SDK started successfully');
} catch (error) {
  console.error('Error starting OpenTelemetry SDK:', error);
  process.exit(1);
}

// Set up Express app
const express = require('express');
const app = express();
const port = 3000;

// Helper function for logging with trace/span IDs
function logWithTraceIds(level, message, context = opentelemetry.context.active()) {
  const span = opentelemetry.trace.getSpan(context);
  const traceId = span ? span.spanContext().traceId : 'no-trace';
  const spanId = span ? span.spanContext().spanId : 'no-span';
  console.log(`[${level}] [TraceID: ${traceId}] [SpanID: ${spanId}] ${message}`);
}

// Middleware for request logging
app.use((req, res, next) => {
  const tracer = opentelemetry.trace.getTracer('example-tracer');
  const span = tracer.startSpan(`middleware-log`);
  logWithTraceIds('INFO', `Request received: ${req.method} ${req.path}`);
  span.end();
  next();
});

// Root route
app.get('/', (req, res) => {
  const activeContext = opentelemetry.context.active();
  const span = opentelemetry.trace.getSpan(activeContext);
  logWithTraceIds('INFO', 'Processing root route', activeContext);
  span.addEvent('Root event');
  span.setAttribute('severity', 'info');
  res.status(200).send('Root route: Welcome to the OpenTelemetry demo app');
  span.end();
});

// Info route: quick success
app.get('/info', (req, res) => {
  const activeContext = opentelemetry.context.active();
  const span = opentelemetry.trace.getSpan(activeContext);
  logWithTraceIds('INFO', 'Processing info route', activeContext);
  span.addEvent('Info event');
  span.setAttribute('severity', 'info');
  res.status(200).send('Info route: Success');
  span.end();
});

// Warn route: medium delay, success
app.get('/warn', (req, res) => {
  const activeContext = opentelemetry.context.active();
  const span = opentelemetry.trace.getSpan(activeContext);
  logWithTraceIds('WARN', 'Processing warn route with delay', activeContext);
  span.addEvent('Warn event');
  span.setAttribute('severity', 'warn');
  setTimeout(() => {
    res.status(200).send('Warn route: Success after delay');
    span.end();
  }, 500);
});

// Error route: immediate error
app.get('/error', (req, res) => {
  const activeContext = opentelemetry.context.active();
  const span = opentelemetry.trace.getSpan(activeContext);
  logWithTraceIds('ERROR', 'Processing error route', activeContext);
  span.addEvent('Error event');
  span.setAttribute('severity', 'error');
  span.setStatus({ code: opentelemetry.SpanStatusCode.ERROR, message: 'Intentional error' });
  res.status(500).send('Error route: Failure');
  span.end();
});

// Slow-error route: long delay, then error
app.get('/slow-error', (req, res) => {
  const activeContext = opentelemetry.context.active();
  const span = opentelemetry.trace.getSpan(activeContext);
  logWithTraceIds('ERROR', 'Processing slow-error route with long delay', activeContext);
  span.addEvent('Slow error event');
  span.setAttribute('severity', 'error');
  setTimeout(() => {
    span.setStatus({ code: opentelemetry.SpanStatusCode.ERROR, message: 'Intentional slow error' });
    res.status(500).send('Slow-error route: Failure after long delay');
    span.end();
  }, 2000);
});

// Start server with error handling
app.listen(port, () => {
  console.log(`App listening at http://localhost:${port}`);
}).on('error', (error) => {
  console.error(`Error starting server on port ${port}:`, error);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  sdk.shutdown()
    .then(() => console.log('SDK shut down successfully'))
    .catch((error) => console.log('Error shutting down SDK', error))
    .finally(() => process.exit(0));
});